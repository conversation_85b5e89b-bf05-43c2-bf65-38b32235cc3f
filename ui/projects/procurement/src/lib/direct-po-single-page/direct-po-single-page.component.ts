import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  OnInit,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AccountDto,
  AdditionalChargeAccountDto,
} from '@j3-prc-catalog/dtos/account-mapping';
import {
  applyCurrencyConversion,
  directPOCheck,
  EXCHANGE_RATE_ERROR,
  PrcFunctionCodes,
  PrcModuleCodes,
  supplant,
} from '@j3-procurement/dtos';
import {
  DirectPoDetailsDto,
  DirectPOItemDto,
  SaveDirectPoResponseDto,
} from '@j3-procurement/dtos/direct-po';
import { IdLabel, Label } from '@j3-procurement/dtos/label';
import {
  ApprovePoDto,
  QuotationChargesDto,
  SaveQuotationItemDto,
} from '@j3-procurement/dtos/quotation';
import { ActionLabel, ActionResultDto } from '@j3-procurement/dtos/workflow';
import {
  DeactivationGuarded,
  localToUTC,
  NotificationService,
  TypedFormGroup,
  UnsubscribeComponent,
  utcToLocal,
} from 'j3-prc-components';
import {
  CentralizedDataService,
  IJbAttachment,
  ITaskMangerDetails,
  JbDatePipe,
  WebApiRequest,
} from 'jibe-components';
import { BehaviorSubject, combineLatest, from, Observable, of } from 'rxjs';
import {
  catchError,
  filter,
  map,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs/operators';
import {
  ePrcErrorMessages,
  ePrcModalMessages,
  ePrcSuccessMessages,
} from '../models/enums/prc-messages.enum';
import {
  getButtonTooltip,
  mergeNewData,
} from '../requisition-single-page/utils';
import {
  ActionHook,
  ApprovalMatrixDetails,
  WorkflowMenuContext,
} from '../workflow/services/types';
import {
  defaultSectionEditMode,
  SectionEditMode,
  SectionKey,
} from './section-edit-mode';
import {
  DirectPoDetailsForm,
  Finance,
  PageState,
  Section,
  SupplierDetails,
  View,
} from './types';

import { Title } from '@angular/platform-browser';
import { CatalogAccountDto } from '@j3-prc-catalog/dtos/catalog';
import { FinanceAdditionalChargeDto } from '@j3-procurement/dtos/finance';
import { RequisitionItemFinanceDto } from '@j3-procurement/dtos/requisition';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import f from 'odata-filter-builder';
import { MenuItem } from 'primeng';
import { SUCCESS } from '../models/constants';
import { DirectPoPermission } from '../models/enums/direct-po-permission.enum';
import { eApiResponseType } from '../models/enums/prc-api-response-type.enum';
import { ePrcRequestEntity } from '../models/enums/prc-request-entity.enum';
import { ePageRoutes } from '../models/enums/prc-routes.enum';
import { ePrcWorklistType } from '../models/enums/prc-worklist-type.enum';
import { IDiscussionWorkflow } from '../models/interfaces/discussion.interface';
import { HeaderStatus } from '../models/interfaces/header-status';
import { HistoryGraphKey } from '../models/interfaces/history-graph-key';
import { QuotationService } from '../quotation/quotation.service';
import { RouteConstants } from '../requisition-single-page/router-constants';
import { statusOptions } from '../requisition-single-page/status-options';
import { ActionValidationResult } from '../requisition-single-page/types';
import { FinanceItemConfigurationBatchProcessor } from '../services/batch-processor/finance-item-configuration-batch-processor';
import { DirectPOService } from '../services/direct-po/direct-po.service';
import { FeedDiscussionService } from '../services/feed-discussion.service';
import { FinanceService } from '../services/finance.service';
import { JCDSService } from '../services/jcds.service';
import { MasterService } from '../services/master/master.service';
import { PermissionService } from '../services/permission/permission.service';
import { PrcSharedService } from '../services/prc-shared.service';
import { RequisitionService } from '../services/requisition/requisition.service';
import { SidebarMenuService } from '../services/sidebar-menu/sidebar-menu.service';
import { FormStateManager } from '../shared/form-state-manager';
import { HeaderButton } from '../shared/generic-header/header-button';
import { HeaderSection } from '../shared/generic-header/header-section';
import { getHeaderSections } from '../shared/single-page-sub-header/utils';
import { getEnvironmentByRecordNumber } from '../utils/entity-environment';
import { WorkflowApmBtnService } from '../workflow/services/workflow-apm-btn.service';
import { DirectPOCostDto } from './direct-po-cost/types';
import { sidebarMenu } from './sidebar-menu';
import { mapDataToSaveDirectPo } from './utils';

// TODO: get rid of this and use requisition-single-page
@Component({
  selector: 'prc-direct-po-single-page',
  templateUrl: './direct-po-single-page.component.html',
  styleUrls: ['./direct-po-single-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    JbDatePipe,
    FinanceItemConfigurationBatchProcessor,
    WorkflowApmBtnService,
  ],
})
export class DirectPoSinglePageComponent
  extends UnsubscribeComponent
  implements OnInit, DeactivationGuarded
{
  public pageState$: BehaviorSubject<PageState> = new BehaviorSubject({
    additionalCharges: [],
    financialItems: [],
    hasInvalidItems: false,
    currencyCode: '',
    supplierStatus: '',
  });
  additionalChargesData: FinanceAdditionalChargeDto[];

  directPo: DirectPoDetailsDto;
  directPOItems: Map<string, SaveQuotationItemDto> = new Map();
  getQuotationUid: string;

  attachmentSection = false;
  financeSection = false;
  historySection = false;
  purchaseOrderSection = false;

  moduleCode = PrcModuleCodes.Procurement;
  feedDetail: IDiscussionWorkflow;
  functionCode = PrcFunctionCodes.DirectPoDetails;
  requisition = 'requisition';
  requisitionUid: string;
  rfqUid: string;
  itemConfigurationData: RequisitionItemFinanceDto[];
  totalCostValue = 0;
  statusGraphKeys: HistoryGraphKey;
  supplierCurrencies: string[] = [];
  supplierInfo: Label;
  deliveryPortSelected: IdLabel;

  public additionalChargeAccountsMap$ = new BehaviorSubject<
    Map<string, AccountDto[]>
  >(new Map());
  public additionalChargesRequest: WebApiRequest;
  public attachmentConfig: IJbAttachment;
  public canEditGeneralInfo: boolean;
  public catalogGLAccounts$ = new BehaviorSubject<
    Record<string, CatalogAccountDto[]>
  >({});
  public directPoFormGroup: TypedFormGroup<DirectPoDetailsForm>;
  public directPOItemsAll: DirectPOItemDto[] = [];
  public financeItemsRequest: WebApiRequest;
  public headerSections: HeaderSection[];
  public sectionEditMode: SectionEditMode = {
    ...defaultSectionEditMode,
    poDetails: true,
  };
  public parentCatalogUids$: Observable<string[]>;

  private isSaving$ = new BehaviorSubject<boolean>(false);

  public approvalMatrixDetails$: Observable<ApprovalMatrixDetails>;
  public headerButtons$: Observable<HeaderButton[]>;
  public headerStatus$: Observable<HeaderStatus>;
  public linkedRecordsDetails: ITaskMangerDetails;
  public reviewMode = false;
  public settingsOptions$: Observable<MenuItem[]>;

  private saveBtn: HeaderButton = {
    buttonClass: 'save',
    buttonType: 'Standard',
    command: () => this.save(),
    label: 'Save',
  };

  private preWfActionMap = new Map<WorkflowType, ActionHook>([
    [
      'APPROVE',
      (_, forceRework) =>
        forceRework ? undefined : this.buildApproveContext(),
    ],
    [
      'REVIEW',
      (_, forceRework) => (forceRework ? undefined : this.sendForApproval()),
    ],
    [
      'CLOSE',
      (_, forceRework) => (forceRework ? undefined : this.closeDirectPo()),
    ],
  ]);

  public permission = DirectPoPermission;
  public currentView: View = 'purchase-order';
  public readonly: boolean;
  public glAccountUids: string[];

  private currencyCodes: string[];
  private formStateManager: FormStateManager<DirectPoDetailsForm>;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly cds: CentralizedDataService,
    private readonly directPOService: DirectPOService,
    private readonly fb: FormBuilder,
    private readonly feedDiscussionService: FeedDiscussionService,
    private readonly financeService: FinanceService,
    private readonly jcdsService: JCDSService,
    private readonly notificationService: NotificationService,
    private readonly permissionService: PermissionService,
    private readonly prcSharedService: PrcSharedService,
    private readonly quotationService: QuotationService,
    private readonly requisitionService: RequisitionService,
    private readonly router: Router,
    private readonly sidebarMenuService: SidebarMenuService<View, Section>,
    private readonly workflowBtnService: WorkflowApmBtnService,
    private titleService: Title,
    private readonly financeItemConfigurationBatchProcessor: FinanceItemConfigurationBatchProcessor,
    private readonly masterService: MasterService
  ) {
    super();
    const directPOCostFormGroup = this.fb.group<DirectPOCostDto>({
      additionalCharges: [],
      currencyCode: [],
      barge: [],
      discount: [],
      freight: [],
      packaging: [],
      truckCharge: [],
    });
    this.directPoFormGroup = this.fb.group<DirectPoDetailsForm>({
      approvalFlow: [],
      assignee: [],
      deliveryDate: [],
      deliveryPort: [{ id: null }],
      department: [],
      description: [],
      poType: [],
      subject: ['', [Validators.required, Validators.maxLength(1000)]],
      supplier: [],
      supplierName: [null, Validators.required],
      supplierQuotationReference: [null, Validators.required],
      titleNumber: [''],
      urgency: [],
      cost: directPOCostFormGroup,
    });
  }

  @HostListener('window:beforeunload')
  canDeactivate(): boolean {
    return !this.hasViewChanges();
  }

  async ngOnInit(): Promise<void> {
    const { snapshot } = this.activatedRoute;
    this.requisitionUid = snapshot.params[RouteConstants.requisitionUid];
    // requests must be defined before calling setPageState
    this.additionalChargesRequest =
      this.financeService.getAdditionalChargesRequest(
        this.requisitionUid,
        ePrcRequestEntity.Requisition
      );
    this.financeItemsRequest = this.financeService.getFinancialItemsRequest(
      this.requisitionUid,
      ePrcRequestEntity.Requisition
    );

    const [canEditGeneralInfo] = await Promise.all([
      this.permissionService.hasPermissions(DirectPoPermission.EditGeneralInfo),
      this.loadDirectPo(),
      this.setPageState(),
    ]);
    this.canEditGeneralInfo = canEditGeneralInfo;

    this.parentCatalogUids$ =
      this.requisitionService.getRequisitionParentCatalogs(this.requisitionUid);

    this.getCatalogGLAccounts();
    this.getAdditionalChargeGLAccounts();

    if (this.requisitionUid) {
      this.initializeAttachments(this.requisitionUid);
    }

    this.initWorkflow();
    this.applyFormRestictions();
    this.loadSidebarMenu();

    this.setFeedDetailAndStatusGraph();
    this.setLinkedRecordsDetails();
  }

  public onDeliveryPortChanged(selectedDeliveryPort: IdLabel): void {
    this.deliveryPortSelected = selectedDeliveryPort;
  }

  public onDirectPOItemChanged(event: SaveQuotationItemDto): void {
    this.directPOItems.set(event.itemUid, event);
  }

  public async onSelectSupplier({
    supplier,
    currencies,
    currentStatus,
  }: SupplierDetails): Promise<void> {
    this.supplierCurrencies = await this.getCurrencies({
      currencies,
      currentStatus,
    });
    this.supplierInfo = supplier;
  }

  public onSendAdditionalChargesData(event): void {
    this.additionalChargesData = event;
  }

  public onSendItemConfigurationData(event): void {
    this.itemConfigurationData = event;
  }

  public setSectionEditMode(key: SectionKey): void {
    this.sectionEditMode = { ...defaultSectionEditMode, [key]: true };
  }

  /**
   * Validates whether the given actionId can be performed.
   * @param actionId - The actionId to be validated.
   * @returns true if the action is valid, false otherwise.
   */
  private actionValid(actionId: WorkflowType): ActionValidationResult {
    if (actionId !== 'REVIEW') {
      return ActionValidationResult.None;
    }
    const pageState = this.pageState$.value;
    if (!pageState) {
      return ActionValidationResult.None;
    }
    const { financialItems, additionalCharges, hasInvalidItems, currencyCode } =
      pageState;

    this.financeService.setAdditionalChargesQuotationMap(additionalCharges);

    const quotationFinanceValid =
      hasInvalidItems ||
      (this.validateFinance(financialItems) &&
        this.financeService.validateAdditionalCharges(additionalCharges) &&
        Boolean(currencyCode));

    if (!quotationFinanceValid) {
      return ActionValidationResult.QuotationFinance;
    }

    if (!this.validateApprovedSuppliers()) {
      return ActionValidationResult.Suppliers;
    }

    return ActionValidationResult.None;
  }

  private validateApprovedSuppliers(): boolean {
    const { supplierStatus } = this.pageState$.value;
    return supplierStatus === 'Approved';
  }

  private applyFormRestictions(): void {
    const fieldsToDisable: (keyof DirectPoDetailsForm)[] = ['titleNumber'];

    if (!this.canEditGeneralInfo) {
      fieldsToDisable.push(
        'deliveryDate',
        'deliveryPort',
        'subject',
        'supplier',
        'supplierQuotationReference'
      );
    }

    fieldsToDisable.forEach((field) =>
      this.directPoFormGroup.get(field).disable()
    );
  }

  /**
   * Builds the context for the "APPROVE" workflow action.
   *
   * This context is passed to `workflowBtnService.runAction()` and sent as `RunActionDto.context`.
   */
  private buildApproveContext(): ApprovePoDto {
    return { approverName: this.cds.userDetails.User_FullName };
  }

  private buildCloseOptions(action: ActionLabel): MenuItem[] {
    const CLOSE_ID = 'CLOSE';
    if (action?.id !== CLOSE_ID) {
      return [];
    }

    const { additional_steps } = action.configDetails ?? {};
    const showCloseOption = additional_steps?.some(
      (step) => step.workflow_action === CLOSE_ID
    );

    const options: MenuItem[] = [];
    if (showCloseOption) {
      options.push({ ...close, command: () => this.closeDirectPo() });
    }
    return options;
  }

  private async buildHeaderMenuItems({
    action,
    state,
  }: ActionResultDto): Promise<MenuItem[]> {
    const menuItems: MenuItem[] = [];
    if (state.isRework) {
      menuItems.push({
        command: () => this.workflowBtnService.reworkAction(),
        label: 'Rework',
      });
    }
    const closeAndMoveItems = await this.buildCloseOptions(action);
    return [...menuItems, ...closeAndMoveItems];
  }

  private async closeDirectPo(): Promise<void> {
    const [{ clientUid }, { jcdsUrl }] = await Promise.all([
      this.prcSharedService.getClientUid().toPromise(),
      this.prcSharedService.getJCDSUrl().toPromise(),
    ]);
    const userInfo = this.cds.userDetails;
    await this.requisitionService.closeRequisition(this.requisitionUid, {
      clientUid,
      jcdsUrl,
      user: {
        userName: userInfo.User_FullName,
        userUid: userInfo.user_uid,
      },
    });
  }

  private discardFinanceChanges(): void {
    this.additionalChargesData = undefined;
    this.itemConfigurationData = undefined;
  }

  private async getAdditionalChargeGLAccounts(): Promise<void> {
    const result = await this.financeService
      .getAdditionalChargeGLAccounts()
      .toPromise();
    this.setAdditionalChargeGLAccounts(result);
  }

  private async getCatalogGLAccounts(): Promise<void> {
    const result = await this.financeService
      .getCatalogsGLAccounts(this.requisitionUid, ePrcRequestEntity.Requisition)
      .toPromise();
    this.catalogGLAccounts$.next(result);
  }

  private async initializeAttachments(id: string): Promise<void> {
    const isEditAttachmentDisabled =
      !(await this.permissionService.hasPermissions(
        DirectPoPermission.EditAttachment
      ));
    this.attachmentConfig = {
      Module_Code: PrcModuleCodes.Procurement,
      Function_Code: PrcFunctionCodes.DirectPoAttachments,
      Key1: id,
      // for some reason jb-components doesn't show actions by default, these are a copy from jb-attachment.json
      actions: [
        {
          name: 'Edit',
          icon: 'icons8-edit',
          disabled: isEditAttachmentDisabled,
        },
        {
          name: 'Delete',
          icon: 'icons8-delete',
          disabled: isEditAttachmentDisabled,
        },
        { name: 'Download', icon: 'icons8-download' },
      ],
    };
  }

  private initWorkflow(): void {
    const context: WorkflowMenuContext = {
      objectUid: this.requisitionUid,
      objectNumber: this.directPo.requisitionNumber,
      vesselId: this.directPo.vesselId,
      vesselUid: this.directPo.vessel.uid,
      wfList: ePrcWorklistType.Requisition,
      functionCode: PrcFunctionCodes.DirectPoDetails,
    };
    this.workflowBtnService.initWorkflow({
      context,
      preRunActionMap: this.preWfActionMap,
    });
    this.setHeaderButtons();
  }

  private isFinanceDataChanged(): boolean {
    return (
      this.additionalChargesData?.length > 0 ||
      this.itemConfigurationData?.length > 0
    );
  }

  private async loadDirectPo(): Promise<void> {
    const directPo = await this.directPOService
      .getDirectPODetailsMatrix(this.requisitionUid)
      .toPromise();

    if (!directPo) {
      return;
    }

    const {
      supplier,
      quotationUid,
      rfqUid,
      currencyCode,
      packaging,
      freight,
      truckCharge,
      barge,
      additionalCharges,
      discount,
      deliveryDate,
      supplierCurrencies,
      totalAmount,
    } = directPo;

    this.directPo = directPo;
    this.supplierCurrencies = await this.getCurrencies({
      currencies: supplierCurrencies,
      currentStatus: supplier?.status,
    });

    this.getQuotationUid = quotationUid;
    this.rfqUid = rfqUid;
    this.totalCostValue = totalAmount;

    this.directPoFormGroup.patchValue({
      ...directPo,
      titleNumber: directPo.requisitionNumber,
      deliveryDate: utcToLocal(deliveryDate),
      supplierName: supplier?.name,
      supplierQuotationReference: directPo?.supplierQuotationReference ?? '',
      cost: {
        currencyCode: currencyCode ?? '',
        packaging: packaging ?? 0,
        freight: freight ?? 0,
        truckCharge: truckCharge ?? 0,
        barge: barge ?? 0,
        additionalCharges: additionalCharges ?? 0,
        discount: discount ?? 0,
      },
    });
    this.formStateManager = new FormStateManager(this.directPoFormGroup);

    this.headerSections = getHeaderSections(this.directPo.vessel?.name);
    this.pageState$.next({
      ...this.pageState$.value,
      currencyCode,
      supplierStatus: supplier?.status,
    });
    this.titleService.setTitle(this.directPo?.requisitionNumber);
  }

  private loadSidebarMenu(): void {
    this.sidebarMenuService.init(sidebarMenu, this.componentDestroyed$);
    this.sidebarMenuService.selectedView$
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((nextView: View) => {
        if (nextView === this.currentView) {
          return;
        }
        if (this.hasViewChanges()) {
          if (!confirm(ePrcModalMessages.UnsavedChanges)) {
            return;
          }
          this.discardViewChanges();
        }
        this.currentView = nextView;
        this.cdr.markForCheck();
      });
  }

  private navigateToRequisition(): void {
    this.router.navigate(
      [ePageRoutes.RequisitionSinglePage, this.requisitionUid],
      { state: { activated: true } }
    );
  }

  private async save(): Promise<void> {
    this.isSaving$.next(true);
    try {
      const { saveDirectPOResult, itemFinanceResponseState } =
        await this.saveDirectPo();

      const { hasInvalidItems, totalAmount } = saveDirectPOResult;
      this.totalCostValue = totalAmount;
      if (hasInvalidItems) {
        throw new Error(ePrcErrorMessages.InvalidItems);
      }
      if (itemFinanceResponseState !== SUCCESS) {
        throw new Error(itemFinanceResponseState);
      }
      this.notificationService.success(ePrcSuccessMessages.RecordSavedSuccess);
    } catch (err) {
      const { message, name } = err.error ?? {};
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message
      );
    } finally {
      this.isSaving$.next(false);
    }
  }

  private async saveDirectPo(): Promise<{
    saveDirectPOResult: SaveDirectPoResponseDto;
    itemFinanceResponseState: string;
  }> {
    const financeData = {
      requisitionItemsConfiguration: this.itemConfigurationData,
      requisitionAdditionalCharges: this.additionalChargesData,
    };

    const { additionalCharges, financialItems } = this.pageState$.value;
    const additionalChargesMerged = mergeNewData(
      additionalCharges,
      this.additionalChargesData,
      'uid'
    );
    const financialItemsMerged = mergeNewData(
      financialItems,
      this.itemConfigurationData,
      'uid'
    );

    const mergedItems = [...additionalChargesMerged, ...financialItemsMerged]
      .map((r) => r?.glAccount?.uid)
      .filter(Boolean);
    this.glAccountUids = [...new Set(mergedItems)];

    this.financeItemConfigurationBatchProcessor.addItems(
      this.itemConfigurationData
    );
    const {
      deliveryDate,
      supplierName,
      supplierQuotationReference,
      description,
      subject,
      deliveryPort,
      titleNumber,
      poType,
      cost,
      ...rest
    } = this.directPoFormGroup.getRawValue();

    const directPOData = mapDataToSaveDirectPo(cost, [
      ...this.directPOItems.values(),
    ]);

    const [saveDirectPOResult, message] = await Promise.all([
      this.directPOService
        .saveDirectPODetails(this.requisitionUid, {
          ...rest,
          deliveryPort: this.deliveryPortSelected ?? this.directPo.deliveryPort,
          reference: supplierQuotationReference,
          deliveryDate: localToUTC(deliveryDate),
          subject,
          rfqUid: this.directPo.rfqUid,
          poType,
          requisitionAdditionalCharges:
            financeData.requisitionAdditionalCharges,
          ...directPOData,
        })
        .toPromise(),
      this.subscribeBatchProcess(),
    ]);
    this.cdr.markForCheck();

    if (!directPOCheck(poType?.type)) {
      this.navigateToRequisition();
    }

    const { freight, packaging, truckCharge, barge } = cost;
    const quotationCharges: QuotationChargesDto = {
      freight: Boolean(+freight),
      packaging: Boolean(+packaging),
      truck: Boolean(+truckCharge),
      barge: Boolean(+barge),
      other: Boolean(+cost.additionalCharges),
      taxation: false,
    };

    this.financeService.setAdditionalChargesQuotationMap(
      additionalCharges,
      quotationCharges
    );
    this.pageState$.next({
      additionalCharges: additionalChargesMerged ?? this.additionalChargesData,
      financialItems: financialItemsMerged ?? this.itemConfigurationData,
      hasInvalidItems: saveDirectPOResult.hasInvalidItems,
      currencyCode: cost.currencyCode,
      supplierStatus: saveDirectPOResult.supplierStatus,
    });
    this.formStateManager.updateOriginalFormValue();
    this.directPoFormGroup.markAsPristine();
    this.additionalChargesData = null;
    this.itemConfigurationData = null;
    this.directPOItems = new Map();
    this.deliveryPortSelected = undefined;

    this.directPo = {
      ...this.directPo,
      totalItems: saveDirectPOResult.totalItems,
    };

    return { saveDirectPOResult, itemFinanceResponseState: message };
  }

  private async sendForApproval(): Promise<void> {
    const poTypeMap = await this.jcdsService.getDataMap('poType');
    const currentPoType = poTypeMap.get(this.directPo.poType.uid)?.types;
    const selectedPoTypeField = this.directPoFormGroup.get('poType').value;
    const selectedPoType = poTypeMap.get(selectedPoTypeField.uid)?.types;

    if (currentPoType !== selectedPoType) {
      this.notificationService.error(ePrcErrorMessages.PurchaseTypeChanged);
      return;
    }
    if (this.directPoFormGroup.invalid) {
      this.notificationService.error(ePrcErrorMessages.MandatoryFields);
      return;
    }

    const { saveDirectPOResult, itemFinanceResponseState } =
      await this.saveDirectPo();

    const { hasInvalidItems, totalAmount } = saveDirectPOResult;
    if (hasInvalidItems) {
      throw new Error(ePrcErrorMessages.InvalidItems);
    }
    if (itemFinanceResponseState !== SUCCESS) {
      throw new Error(itemFinanceResponseState);
    }
    const [{ clientUid }, { jcdsUrl }] = await Promise.all([
      this.prcSharedService.getClientUid().toPromise(),
      this.prcSharedService.getJCDSUrl().toPromise(),
    ]);
    const userInfo = this.cds.userDetails;
    const { currencyRates } = await this.quotationService
      .initiateApprovalProccess(this.requisitionUid, {
        clientUid,
        jcdsUrl,
        user: { userName: userInfo.User_FullName, userUid: userInfo.user_uid },
      })
      .toPromise();

    const {
      cost: { currencyCode },
    } = this.directPoFormGroup.value;
    const rate = currencyRates?.find(
      ({ Curr_Code }) => Curr_Code === currencyCode
    );
    if (!rate) {
      throw new Error(supplant(EXCHANGE_RATE_ERROR, { currencyCode }));
    }
    this.totalCostValue = applyCurrencyConversion(totalAmount, rate.Exch_rate);
  }

  private setAdditionalChargeGLAccounts(
    additionalChargeAccounts: AdditionalChargeAccountDto[]
  ): void {
    const chargeAccounts: Map<string, AccountDto[]> =
      additionalChargeAccounts.reduce(
        (acc, { accounts, uid }) => acc.set(uid, accounts),
        new Map<string, AccountDto[]>()
      );

    this.additionalChargeAccountsMap$.next(chargeAccounts);
  }

  private setFormState(): void {
    if (this.readonly || this.reviewMode) {
      this.directPoFormGroup.disable();
    } else {
      this.directPoFormGroup.enable();
      this.applyFormRestictions();
    }
    this.cdr.markForCheck();
  }

  private setFeedDetailAndStatusGraph(): void {
    const entityEnvironment = getEnvironmentByRecordNumber(
      this.directPo.requisitionNumber
    );
    this.feedDetail = this.feedDiscussionService.getDetails(
      this.requisitionUid,
      this.directPo.vesselId,
      entityEnvironment,
      ePrcWorklistType.Po,
      this.directPo.requisitionNumber
    );

    this.statusGraphKeys = {
      key1: this.requisitionUid,
      key2: entityEnvironment.toString(),
      key3: this.directPo.vesselId?.toString(),
    };
  }

  private setHeaderButtons(): void {
    this.headerButtons$ = combineLatest([
      this.workflowBtnService.getWfButton(),
      this.isSaving$,
      this.pageState$,
    ]).pipe(
      map(([wfButton, isSaving]) => {
        const saveDisabled = isSaving || this.readonly;
        const saveBtn = { ...this.saveBtn, disabled: saveDisabled };
        if (!wfButton) {
          return [saveBtn];
        }
        const actionId = wfButton.id as WorkflowType;
        const actionValid = this.actionValid(actionId);
        const disabled =
          wfButton.disabled || actionValid !== ActionValidationResult.None;
        // customise default tooltip only when the action is invalid.
        const tooltip = getButtonTooltip(actionValid, wfButton);
        return [saveBtn, { ...wfButton, disabled, tooltip }];
      }),
      startWith([{ ...this.saveBtn, disabled: this.readonly }])
    );

    this.headerStatus$ = this.workflowBtnService.wfState$.pipe(
      filter((state) => Boolean(state)),
      tap((state) => this.updatePageMode(state?.id)),
      map(({ id, name }) => ({
        color: statusOptions[id]?.color,
        text: name ?? id,
      }))
    );

    this.approvalMatrixDetails$ = this.workflowBtnService.wfAction$.pipe(
      filter((res) => Boolean(res)),
      map(({ apm }) => {
        if (!apm?.nextApproverRoles?.length) {
          return null;
        }
        const { approvalFlowName, nextApproverRoles } = apm;
        return { approvalFlowName, nextApproverRoles };
      })
    );

    this.settingsOptions$ = this.workflowBtnService.wfAction$.pipe(
      filter((actionRes) => Boolean(actionRes)),
      switchMap((actionRes) =>
        from(this.buildHeaderMenuItems(actionRes)).pipe(
          catchError(() => of([]))
        )
      )
    );
  }

  private setLinkedRecordsDetails(): void {
    this.linkedRecordsDetails = {
      function_code: this.functionCode,
      module_code: this.moduleCode,
      uid: this.requisitionUid,
      Vessel_ID: this.directPo.vesselId,
      Vessel_Name: this.directPo.vessel.name,
      vessel_uid: this.directPo.vessel.uid,
      WL_TYPE: ePrcWorklistType.Requisition,
    };
  }

  private async setPageState(): Promise<void> {
    const [financialItems, additionalCharges, hasInvalidItems] =
      await combineLatest([
        this.financeService.getFinancialItems(this.financeItemsRequest),
        this.financeService.getAdditionalCharges(this.additionalChargesRequest),
        this.directPOService.validateDirectPOItems(this.requisitionUid),
        this.financeService.getRequiredQuotationCharges(this.requisitionUid),
      ]).toPromise();

    this.pageState$.next({
      ...this.pageState$.value,
      additionalCharges,
      financialItems,
      hasInvalidItems,
    });
    this.financeService.setAdditionalChargesQuotationMap(additionalCharges);
  }

  private subscribeBatchProcess(): Promise<string> {
    const batchPromise =
      this.financeItemConfigurationBatchProcessor.$financeBatchProcessorState
        .pipe(take(1))
        .toPromise();
    this.financeItemConfigurationBatchProcessor.flush();
    return batchPromise;
  }

  private updatePageMode(stateId: WorkflowType): void {
    if (!stateId) {
      return;
    }
    this.reviewMode = ['REVIEW', 'APPROVE'].includes(stateId);
    this.readonly = stateId === 'CLOSE';
    this.directPOService.isReadonly = this.readonly || this.reviewMode;
    this.setFormState();
  }

  private validateFinance(list: Finance[] = []): boolean {
    return list.length && list.every(({ glAccount }) => glAccount?.uid);
  }

  private isDirectPoDetailsChanged(): boolean {
    const directPOItemChanged = this.directPOItems.size > 0;
    return (
      directPOItemChanged ||
      this.formStateManager.hasViewChanges() ||
      Boolean(this.deliveryPortSelected)
    );
  }

  private discardDirectPoDetails(): void {
    this.formStateManager.discardViewChanges();
    this.directPOItems = new Map();
    this.deliveryPortSelected = undefined;
    this.supplierCurrencies = this.directPo.supplierCurrencies;
  }

  private discardViewChanges(): void {
    if (this.currentView === 'finance') {
      return this.discardFinanceChanges();
    }
    return this.discardDirectPoDetails();
  }

  private hasViewChanges(): boolean {
    if (this.currentView === 'finance') {
      return this.isFinanceDataChanged();
    }
    if (this.currentView === 'purchase-order') {
      return this.isDirectPoDetailsChanged();
    }
    return false;
  }

  private async getCurrencies({
    currencies,
    currentStatus,
  }: SupplierDetails): Promise<string[]> {
    return currentStatus === 'Approved'
      ? currencies
      : await this.getCurrencyCodes();
  }

  private async getCurrencyCodes(): Promise<string[]> {
    if (!this.currencyCodes) {
      try {
        const currencyRates = await this.masterService.getDataSource(
          'currencyRates',
          {
            select: 'Curr_Code',
            filter: f().eq('active_status', true),
          }
        );
        this.currencyCodes = currencyRates.map((r) => r.Curr_Code);
      } catch (err) {
        this.notificationService.error(err?.message);
      }
    }

    return this.currencyCodes;
  }
}
