import { Injectable } from '@angular/core';
import { ApprovalMatrixDto } from '@j3-approval-matrix/dtos/approval-matrix';
import { Subject } from 'rxjs';
import { ApprovalCommandParams } from '../approval-flow-popup/types';

@Injectable()
export class ApmDialogService {
  public openDialogSubject = new Subject<{
    flowList: ApprovalMatrixDto[];
    isHistory?: boolean;
    command: (params: ApprovalCommandParams) => void;
    reject: () => void;
  }>();

  public openDialog(params: {
    flowList: ApprovalMatrixDto[];
    isHistory?: boolean;
  }): Promise<ApprovalCommandParams> {
    return new Promise((resolve) => {
      const command = (data: ApprovalCommandParams) => resolve(data);

      const reject = () => resolve(undefined);
      this.openDialogSubject.next({ ...params, command, reject });
    });
  }
}
