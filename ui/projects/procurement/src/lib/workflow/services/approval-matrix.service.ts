import {
  ApprovalFlowApproverDto,
  ApprovalFlowAssigneeDto,
} from '@j3-approval-matrix/dtos/approval-flow';
import {
  ApprovalMatrixDto,
  ApprovalMatrixRequestDto,
} from '@j3-approval-matrix/dtos/approval-matrix';
import {
  ApprovalHistoryState,
  ApprovalPublicInitDto,
  ApprovalPublicInitResponse,
} from '@j3-approval-matrix/dtos/approval-service';
import {
  HistoryFlowDto,
  HistoryInitDto,
} from '@j3-approval-matrix/dtos/history';
import { ApiRequestService, eCrud, WebApiRequest } from 'jibe-components';
import { Observable, of } from 'rxjs';

import { Injectable } from '@angular/core';
import { supplant } from '@j3-procurement/dtos';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';

@Injectable()
export class ApprovalMatrixService {
  constructor(private apiRequest: ApiRequestService) {}

  approveFlow(
    approvalFlowUid: string,
    payload?: HistoryInitDto
  ): Observable<{
    state: ApprovalHistoryState;
    approvers: ApprovalFlowApproverDto[];
    nextApprover: string;
    assignee: ApprovalFlowAssigneeDto[];
  }> {
    const approveStep: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrix,
      action: supplant(ePrcRequestAction.ApproveFlowStep, {
        historyUid: approvalFlowUid,
      }),
      crud: eCrud.Post,
      body: payload,
    };
    return this.apiRequest.sendApiReq(approveStep);
  }

  canApproveFlow(approvalFlowUid: string): Observable<boolean> {
    const req: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrix,
      action: supplant(ePrcRequestAction.ApproveFlowStep, {
        historyUid: approvalFlowUid,
      }),
      crud: eCrud.Get,
    };
    return approvalFlowUid ? this.apiRequest.sendApiReq(req) : of();
  }

  getApprovalFlowList(
    body: ApprovalMatrixRequestDto
  ): Observable<ApprovalMatrixDto[]> {
    const approveStep: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrixV2,
      action: ePrcRequestAction.GetApprovalFlowList,
      crud: eCrud.Post,
      body,
    };
    return this.apiRequest.sendApiReq(approveStep);
  }

  getFlowState(approvalFlowUid: string): Observable<ApprovalHistoryState> {
    const getFlowStepsApiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrix,
      action: supplant(ePrcRequestAction.GetFlowState, {
        historyUid: approvalFlowUid,
      }),
      crud: eCrud.Get,
    };
    return this.apiRequest.sendApiReq(getFlowStepsApiReq);
  }

  getHistoryFlow(objectUid: string): Observable<HistoryFlowDto> {
    const createNewFlowApiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrixV2,
      action: supplant(ePrcRequestAction.GetApprovalFlow, { objectUid }),
      crud: eCrud.Get,
    };
    // ensure the error is thrown to prevent unintended side effects
    return this.apiRequest.sendApiReq(createNewFlowApiReq, null, (e) => {
      throw e;
    });
  }

  initFlow(body: HistoryInitDto): Observable<HistoryFlowDto> {
    const createNewFlowApiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrixV2,
      action: 'flow-init',
      crud: eCrud.Post,
      body,
    };
    return this.apiRequest.sendApiReq(createNewFlowApiReq);
  }

  getFlowUid(
    body: ApprovalPublicInitDto
  ): Observable<ApprovalPublicInitResponse> {
    const createNewFlowApiReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ApprovalMatrixAPI,
      entity: ePrcRequestEntity.ApprovalMatrix,
      action: ePrcRequestAction.CreateApprovalFlow,
      crud: eCrud.Post,
      body,
    };
    return this.apiRequest.sendApiReq(createNewFlowApiReq);
  }
}
