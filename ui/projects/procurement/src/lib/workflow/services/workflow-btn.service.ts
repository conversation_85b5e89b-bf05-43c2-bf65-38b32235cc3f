import { Injectable } from '@angular/core';
import {
  ePrcWorklistType,
  PrcFunctionCodes,
  PrcModuleCodes,
  TASK_IN_PROGRESS,
} from '@j3-procurement/dtos';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import {
  ActionResultDto,
  RunActionDto,
  StateLabel,
} from '@j3-procurement/dtos/workflow';
import { NotificationService } from 'j3-prc-components';
import { eAppLocation, UserService } from 'jibe-components';
import {
  BehaviorSubject,
  combineLatest,
  concat,
  EMPTY,
  from,
  Observable,
  of,
} from 'rxjs';
import { filter, map, shareReplay, switchMap } from 'rxjs/operators';

import { eApiResponseType } from '../../models/enums/prc-api-response-type.enum';
import { ePrcErrorMessages } from '../../models/enums/prc-messages.enum';
import { MasterService } from '../../services/master/master.service';
import { PermissionService } from '../../services/permission/permission.service';
import { TaskStatusService } from '../../services/task-status/task-status.service';
import { HeaderButton } from '../../shared/generic-header/header-button';
import { InitiateWorkflowParams, WorkflowMenuContext } from './types';
import { RunActionOptions } from './types';
import { ActionHook } from './types';
import { WorkflowService } from './workflow.service';

/**
 * This service should be injected at the component level to ensure
 * each component instance maintains its own cached values,
 * preventing unintended data sharing across modules.
 */
@Injectable()
export class WorkflowBtnService<T extends ActionResultDto = ActionResultDto> {
  protected actionSubject = new BehaviorSubject<T>(null);
  protected runningSubject = new BehaviorSubject<boolean>(false); // Track running state

  protected context: WorkflowMenuContext;
  private preRunActionMap: Map<WorkflowType, ActionHook>;
  private postRunActionMap: Map<WorkflowType, ActionHook>;

  public wfAction$ = this.actionSubject.asObservable();
  public wfState$ = this.actionSubject.asObservable().pipe(
    filter((res) => Boolean(res)),
    map(({ state }) => state)
  );

  constructor(
    protected masterService: MasterService,
    protected notificationService: NotificationService,
    protected permissionService: PermissionService,
    protected taskStatusService: TaskStatusService,
    protected userService: UserService,
    protected workflowService: WorkflowService
  ) {}

  /**
   * Returns an observable that emits a workflow-related `HeaderButton`.
   * - If no `objectUid` is found in the context, emits nothing and logs a warning.
   * - If a workflow action is inProgress, emits a disabled button first,
   *   then polls the task status until complete and emits an updated button.
   * - Otherwise, emits an enabled or disabled button based on `canRunAction`, `runningSubject`, and tooltip.
   *
   * The result is memoized with `shareReplay(1)` so multiple subscribers get the same result.
   */
  public getWfButton(): Observable<HeaderButton> {
    const { objectUid } = this.context ?? {};
    if (!objectUid) {
      console.warn(
        '[getWfButton] Workflow not initialized: objectUid is missing. Ensure initWorkflow() has been called.'
      );
      return EMPTY;
    }

    const buildWfButton$ = (res: T): Observable<HeaderButton> => {
      const canRun$ = from(this.canRunAction(res));
      const tooltip$ = from(this.getWfTooltip(res));
      return combineLatest([canRun$, tooltip$, this.runningSubject]).pipe(
        map(([canRun, tooltip, running]) =>
          this.buildHeaderButton(res, !canRun || running, tooltip)
        )
      );
    };

    const loadActionState$ = (): Observable<T> =>
      this.workflowService.getActionState(objectUid).pipe(
        switchMap((state: T) => from(this.initActionResult(state))),
        switchMap(() => this.actionSubject),
        filter((res) => Boolean(res))
      );

    return loadActionState$().pipe(
      switchMap((res) => {
        if (res.inProgress) {
          const disabled$ = of(
            this.buildHeaderButton(res, true, TASK_IN_PROGRESS)
          );
          const enabled$ = from(
            this.taskStatusService.waitForDone(objectUid)
          ).pipe(
            switchMap(() =>
              loadActionState$().pipe(
                switchMap((lastRes) => buildWfButton$(lastRes))
              )
            )
          );
          return concat(disabled$, enabled$);
        }
        return buildWfButton$(res);
      }),
      shareReplay(1)
    );
  }

  public getState(): StateLabel {
    return this.actionSubject.getValue()?.state;
  }

  public initWorkflow(params: InitiateWorkflowParams): void {
    const { context, preRunActionMap, postRunActionMap } = params;
    if (!context?.objectUid) {
      console.warn('Argument (context.objectUid) cannot be null or undefined');
      return;
    }
    this.context = context;
    this.preRunActionMap = preRunActionMap;
    this.postRunActionMap = postRunActionMap;
  }

  public reworkAction(): Promise<ActionResultDto> {
    const state = this.actionSubject.getValue()?.state;
    return state?.prevId
      ? this.runAction(state.prevId, { forceRework: true })
      : Promise.resolve({});
  }

  /**
   * Executes a workflow action by updating the status of an object and handling pre/post actions.
   *
   * @param statusId - The new status to set for the workflow.
   * @param options - Optional settings for the action:
   *   - `beforeRun`: Optional callback executed before running the action.
   *   - `forceRework` (default: false): Forces rework even if normally restricted.
   *   - `reason`: Optional reason for the status change.
   * @returns Resolves with an object containing `postResult` and `preResult`.
   * ### Behavior:
   * 1. Retrieves the current actionResult and prepares the request payload (`RunActionDto`).
   * 2. Calls `workflowService.markTransition()` to register the state change attempt.
   * 3. Triggers a pre-run action (if defined). If it returns `false`, the process stops.
   * 4. The `preResult` (result of pre-run action) is used as the `context` in `workflowService.runAction()`.
   * 5. Executes `beforeRun` callback if provided. If it returns `false`, the process stops.
   * 6. Calls `workflowService.runAction()` to perform the actual state transition.
   * 7. Updates the `actionSubject` with the new action, state, and timestamp.
   * 8. Triggers a post-run action (if defined).
   * 9. On error:
   *    - Displays a notification with an appropriate error message.
   * 10. Regardless of success or failure, resets `runningSubject` to `false` and rolls back the transition mark.
   */
  public async runAction<PreResult = undefined, PostResult = undefined>(
    statusId: WorkflowType,
    options?: RunActionOptions<T>
  ): Promise<ActionResultDto<PreResult, PostResult>> {
    const { beforeRun, forceRework, reason } = options ?? {};
    const { objectUid, vesselUid, wfList } = this.context;
    let errorName: string | undefined;
    this.runningSubject.next(true);
    try {
      let actionResult = this.actionSubject.getValue();
      const startTs = await this.workflowService.markTransition(objectUid);
      const preRunAction = this.preRunActionMap?.get(statusId);

      const context = await preRunAction?.(actionResult, forceRework);
      if (context === false) {
        return;
      }

      const midResult = await beforeRun?.();
      if (midResult === false) {
        return;
      }

      actionResult = midResult ?? actionResult;
      const { action, postResult, preResult, state, timestamp } =
        await this.runActionPool({
          context,
          forceRework,
          objectUid,
          reason,
          statusId,
          timestamp: startTs,
          vesselUid,
          wlType: wfList,
        });
      this.actionSubject.next({ ...actionResult, action, state, timestamp });
      this.saveWorkFlowEvents(statusId);
      const postAction = this.postRunActionMap?.get(statusId);
      await postAction?.(actionResult, forceRework);
      return { postResult, preResult };
    } catch (err) {
      const { message, name } = err.error ?? {};
      errorName = name;
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message || err.message
      );
    } finally {
      if (errorName !== eApiResponseType.TaskInProgressError) {
        this.workflowService.markTransition(objectUid, false);
      }
      this.runningSubject.next(false);
    }
  }

  protected buildHeaderButton(
    { action }: T,
    disabled?: boolean,
    tooltip?: string
  ): HeaderButton {
    return action
      ? {
          id: action.id,
          buttonType: 'Standard',
          command: () => this.runAction(action.id),
          disabled,
          label: action.name,
          tooltip,
        }
      : null;
  }

  protected canRunAction(res: T): Promise<boolean> {
    const action = res.action;
    return action?.rightCode
      ? this.permissionService.hasPermissions(action.rightCode)
      : Promise.resolve(true);
  }

  protected getWfTooltip(_: T): Promise<string> {
    // overridden in subclasses
    return Promise.resolve('');
  }

  protected initActionResult(initial: T): Promise<void> {
    this.actionSubject.next(initial);
    return Promise.resolve();
  }

  protected isOffice(): number {
    const { AppLocation } = this.userService.getUserDetails() ?? {};
    return AppLocation === eAppLocation.Office ? 1 : 0;
  }

  private async runActionPool(dto: RunActionDto): Promise<ActionResultDto> {
    await this.workflowService.runAction(dto, true);
    await this.taskStatusService.waitForDone(dto.objectUid);
    return this.workflowService.getActionState(dto.objectUid).toPromise();
  }

  // TODO: Move to backend
  private async saveWorkFlowEvents(actionId: WorkflowType): Promise<any> {
    const { objectNumber, objectUid, vesselId, vesselUid, wfList } =
      this.context;

    const details = {
      job_card_no: objectNumber,
      vessel_uid: vesselUid,
      wl_type: wfList,
    };

    const wfListName =
      wfList === ePrcWorklistType.Po ? 'Purchase Order' : wfList;

    const body = {
      key1: objectUid,
      key2: this.isOffice(),
      key3: vesselId,
      key4: this.userService.getUserDetails()?.Company_Id,
      module_code: PrcModuleCodes.Procurement,
      function_code: PrcFunctionCodes.Requisition,
      remark: `${wfListName} Status Updated`,
      remark_type: 1,
      sync_to: vesselId,
      action_code: actionId,
      details: JSON.stringify(details),
    };
    await this.masterService.saveWorkflowEventsReq(body);
  }
}
