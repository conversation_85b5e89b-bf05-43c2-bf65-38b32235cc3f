import { HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ePrcWorklistType } from '@j3-procurement/dtos';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import {
  ActionLabel,
  ActionResultDto,
  RunActionDto,
} from '@j3-procurement/dtos/workflow';
import { ApiRequestService, eCrud } from 'jibe-components';
import { Observable } from 'rxjs';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';

@Injectable()
export class WorkflowService {
  constructor(private apiRequestService: ApiRequestService) {}

  findAction(
    taskType: ePrcWorklistType,
    statusId: WorkflowType
  ): Observable<ActionLabel> {
    return this.apiRequestService.sendApiReq({
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Workflow,
      action: `wf-actions/${taskType}/${statusId}`,
      crud: eCrud.Get,
    });
  }

  getActionState(
    objectUid: string,
    useTmStatus = true
  ): Observable<ActionResultDto> {
    const httpParams = new HttpParams().set('useTmStatus', String(useTmStatus));
    return this.apiRequestService.sendApiReq({
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Workflow,
      action: objectUid,
      crud: eCrud.Get,
      params: httpParams.toString(),
    });
  }

  /**
   * Initiates the transition process. This must be called before `runAction` to prevent race conditions.
   */
  markTransition(objectUid: string, inProgress = true): Promise<Date> {
    return this.apiRequestService
      .sendApiReq({
        apiBase: ePrcRequestApiBase.j3ProcurementAPI,
        entity: ePrcRequestEntity.Workflow,
        action: objectUid,
        crud: eCrud.Put,
        body: { inProgress },
      })
      .toPromise();
  }

  /**
   * Runs an action immediately or queues it for processing (with pool = true).
   */
  runAction<T extends boolean>(
    body: RunActionDto,
    pool: T
  ): Promise<T extends true ? void : ActionResultDto> {
    return this.apiRequestService
      .sendApiReq({
        apiBase: ePrcRequestApiBase.j3ProcurementAPI,
        entity: ePrcRequestEntity.Workflow,
        action: pool ? ePrcRequestAction.ActionQueue : '',
        body,
        crud: eCrud.Post,
      })
      .toPromise();
  }
}
