import {
  ApprovalFlowApproverRoleDto,
  ApprovalFlowAssigneeDto,
} from '@j3-approval-matrix/dtos/approval-flow';

import { ApprovalPublicStepInfoDto } from '@j3-approval-matrix/dtos/approval-service';
import { MaybePromise } from '@j3-prc-shared/dtos';
import { ePrcWorklistType, PrcFunctionCodes } from '@j3-procurement/dtos';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import { ActionResultDto } from '@j3-procurement/dtos/workflow';

export type ActionHook = (
  result: ActionResultDto,
  forceRework?: boolean
) => MaybePromise<unknown>;

export type ApmActionResult = ActionResultDto & { apm: ApprovalMatrixDetails };

export interface ApprovalMatrixDetails {
  approvalFlowName: string;
  approvalHistoryUid?: string;
  currentStep?: ApprovalPublicStepInfoDto;
  nextSteps?: ApprovalPublicStepInfoDto[];
  nextApproverRoles: ApprovalFlowApproverRoleDto[];
  nextApprover?: string;
  assignees?: ApprovalFlowAssigneeDto[];
}

export interface RunActionOptions<T extends ActionResultDto> {
  forceRework?: boolean;
  reason?: string;
  beforeRun?: () => MaybePromise<T | false>; // Optional hook to run before executing the action
}

export interface InitiateWorkflowParams {
  preRunActionMap?: Map<WorkflowType, ActionHook>;
  postRunActionMap?: Map<WorkflowType, ActionHook>;
  context: WorkflowMenuContext;
}

// TODO reduce the size of WorkflowMenuContext
export interface WorkflowMenuContext {
  objectUid: string;
  objectNumber: string;
  vesselId: number;
  vesselUid: string;
  wfList: ePrcWorklistType;
  functionCode?:
    | PrcFunctionCodes.RequisitionDetails
    | PrcFunctionCodes.DirectPoDetails;
}
