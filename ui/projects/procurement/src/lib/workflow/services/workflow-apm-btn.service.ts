import { NotificationService, RolesPipe } from 'j3-prc-components';
import {
  apmPermissionTooltip,
  APM_BUTTON_NAME,
  noApprovalFlows,
} from './constants';
import {
  ApmActionResult,
  ApprovalMatrixDetails,
  RunActionOptions,
} from './types';

import { Injectable } from '@angular/core';
import { ApprovalMatrixRequestDto } from '@j3-approval-matrix/dtos/approval-matrix';
import { HistoryInitDto } from '@j3-approval-matrix/dtos/history';
import { supplant } from '@j3-prc-shared/dtos';
import { PrcModuleCodes } from '@j3-procurement/dtos';
import { WorkflowType } from '@j3-procurement/dtos/task-status';
import { ActionResultDto } from '@j3-procurement/dtos/workflow';
import { UserService } from 'jibe-components';
import { MasterService } from '../../services/master/master.service';
import { PermissionService } from '../../services/permission/permission.service';
import { TaskStatusService } from '../../services/task-status/task-status.service';
import { HeaderButton } from '../../shared/generic-header/header-button';
import { ApmDialogService } from './apm-dialog.service';
import { ApprovalMatrixService } from './approval-matrix.service';
import { WorkflowBtnService } from './workflow-btn.service';
import { WorkflowService } from './workflow.service';

@Injectable()
export class WorkflowApmBtnService extends WorkflowBtnService<ApmActionResult> {
  private apmReq: ApprovalMatrixRequestDto;

  set apmContext(value: ApprovalMatrixRequestDto) {
    this.apmReq = value;
  }

  constructor(
    private readonly apmDialogService: ApmDialogService,
    private readonly approvalMatrixService: ApprovalMatrixService,
    private readonly rolePipe: RolesPipe,
    protected masterService: MasterService,
    protected notificationService: NotificationService,
    protected permissionService: PermissionService,
    protected taskStatusService: TaskStatusService,
    protected userService: UserService,
    protected workflowService: WorkflowService
  ) {
    super(
      masterService,
      notificationService,
      permissionService,
      taskStatusService,
      userService,
      workflowService
    );
  }

  public async reworkAction(): Promise<ActionResultDto> {
    const curState = this.actionSubject.getValue()?.state;
    // If there's no previous state, exit early (nothing to revert to).
    if (!curState?.prevId) {
      return;
    }
    await this.runAction(curState.prevId, { forceRework: true });
    const { action, apm, state, timestamp } = this.actionSubject.getValue();
    const isApm = state.configDetails?.approval_matrix?.is_applicable;
    let newApm = apm;
    // If the APM is applicable, load the APM details.
    if (isApm) {
      newApm = await this.loadApm();
    } else if (apm) {
      newApm = null;
    }
    // Only update if APM has changed
    if (newApm !== apm) {
      this.actionSubject.next({ action, apm: newApm, state, timestamp });
    }
  }

  public async runAction(
    statusId: WorkflowType,
    options?: RunActionOptions<ApmActionResult>
  ): Promise<ActionResultDto | undefined> {
    const { forceRework } = options ?? {};
    let runOptions = options;
    if (!forceRework) {
      const { action, state } = this.actionSubject.getValue() ?? {};
      const isApm = state.configDetails?.approval_matrix?.is_applicable;
      const nextIsApm = action?.configDetails?.approval_matrix?.is_applicable;
      // if APM is applicable and the action is successfully executed, exit early (no state update needed)
      if (isApm && (await this.runApmAction())) {
        return;
      }
      // if the next action requires APM, initialize the approval flow
      if (nextIsApm) {
        runOptions = {
          ...options,
          beforeRun: () => this.initApmFlow(statusId),
        };
      }
    }
    return super.runAction(statusId, runOptions);
  }

  protected buildHeaderButton(
    actionRes: ApmActionResult,
    disabled?: boolean,
    tooltip?: string
  ): HeaderButton {
    const btn = super.buildHeaderButton(actionRes, disabled, tooltip);
    const { apm, state } = actionRes ?? {};
    const isApm = state?.configDetails?.approval_matrix?.is_applicable;
    return btn && isApm && apm?.currentStep
      ? { ...btn, label: APM_BUTTON_NAME }
      : btn;
  }

  protected async canRunAction(res: ApmActionResult): Promise<boolean> {
    const { apm, state } = res;
    const isApm = state.configDetails?.approval_matrix?.is_applicable;
    if (!(isApm && apm?.currentStep)) {
      return super.canRunAction(res);
    }

    try {
      return await this.approvalMatrixService
        .canApproveFlow(apm.approvalHistoryUid)
        .toPromise();
    } catch (err) {
      this.notificationService.error(err.error?.message || err.message);
      return false;
    }
  }

  protected async getWfTooltip(res: ApmActionResult): Promise<string> {
    const { action, apm, state } = res;
    if (!action) {
      return '';
    }
    if (!state.configDetails?.approval_matrix?.is_applicable) {
      return super.getWfTooltip(res);
    }

    const roles = apm?.nextApproverRoles ?? [];
    if (roles.length === 0) {
      return '';
    }

    const roleNames = await this.rolePipe.transform(roles).toPromise();
    return supplant(apmPermissionTooltip, { roleNames });
  }

  protected async initActionResult(result: ApmActionResult): Promise<void> {
    const { state } = result ?? {};
    let apmResult = result;

    const isApm = state?.configDetails?.approval_matrix?.is_applicable;
    if (isApm) {
      apmResult = { ...result, apm: await this.loadApm() };
    }
    return super.initActionResult(apmResult);
  }

  private async initApmFlow(
    statusId: WorkflowType
  ): Promise<ApmActionResult | false> {
    const flowList = await this.approvalMatrixService
      .getApprovalFlowList(this.apmReq)
      .toPromise();

    if (!flowList?.length) {
      throw new Error(noApprovalFlows);
    }

    // openDialog returns approvalFlowUid if modal is closed successfully, or undefined otherwise.
    const result = await this.apmDialogService.openDialog({
      flowList,
    });
    const { approvalFlowUid, assigneeUid, assigneeName, remarks } = result;

    if (!approvalFlowUid) {
      return false; // abort super.runAction
    }
    const { objectUid, vesselId, objectNumber, functionCode } = this.context;

    const requestDetails: HistoryInitDto = {
      objectUid,
      vesselId,
      objectNumber,
      approvalFlowUid,
      assigneeUid,
      assigneeName,
      remarks,
      value: this.apmReq.value,
      vesselUid: this.apmReq.vesselUid,
      workflowTypeId: statusId,
      moduleCode: PrcModuleCodes.Procurement,
      functionCode,
    };

    const flow = await this.approvalMatrixService
      .initFlow(requestDetails)
      .toPromise();

    const { action, state, timestamp } = this.actionSubject.getValue();
    const apm: ApprovalMatrixDetails = {
      approvalFlowName: flow.name,
      approvalHistoryUid: flow.uid,
      currentStep: flow.state?.currentStep,
      nextSteps: flow.state?.nextSteps,
      nextApproverRoles: flow.state?.currentStep?.info?.roles,
      nextApprover:
        flow.state.nextSteps.length > 0
          ? flow.state.nextSteps[0].info.roles.map((r) => r.role).join(' /')
          : '',
      assignees: flow.state?.nextSteps[0]?.assignees,
    };
    return { action, apm, state, timestamp };
  }

  private async loadApm(): Promise<ApprovalMatrixDetails> {
    try {
      const flow = await this.approvalMatrixService
        .getHistoryFlow(this.context.objectUid)
        .toPromise();

      return {
        approvalFlowName: flow.name,
        approvalHistoryUid: flow.uid,
        currentStep: flow.state?.currentStep,
        nextSteps: flow.state?.nextSteps,
        nextApproverRoles: flow.state?.currentStep?.info?.roles, // shown in single page header
        nextApprover:
          flow.state.nextSteps.length > 0
            ? flow.state.nextSteps[0].info.roles.map((r) => r.role).join(' /')
            : '', // shown in dialog when we clcik on  Approve Po
      };
    } catch (err) {
      this.notificationService.error(err.error?.message || err.message);
    }
  }

  /**
   * Executes the APM action.
   * Returns `true` to indicate that the action was handled and no state update is needed,
   * regardless of success or failure. Returns `false` only if the action was not processed.
   */
  private async runApmAction(): Promise<boolean> {
    const { action, apm, state, timestamp } =
      this.actionSubject.getValue() ?? {};
    if (!apm?.currentStep) {
      return false;
    }
    const shouldOpenDialog = apm.nextSteps.length > 0;
    let dialogResult: any = null;
    if (shouldOpenDialog) {
      dialogResult = await this.apmDialogService.openDialog({
        flowList: [
          {
            uid: apm.currentStep.info.uid,
            name: apm.approvalFlowName,
            nextApprover: apm.nextApprover,
            approvers: [],
            assignees: apm.nextSteps[0]?.assignees,
          },
        ],
        isHistory: true,
      });

      if (!dialogResult) {
        return false;
      } // Dialog was closed
    }

    try {
      const { objectNumber, objectUid, vesselId, vesselUid } = this.context;

      const payload: HistoryInitDto = shouldOpenDialog
        ? {
            objectUid,
            objectNumber,
            vesselId,
            vesselUid,
            approvalFlowUid: apm.approvalHistoryUid,
            remarks: dialogResult?.remarks,
            assigneeUid: dialogResult?.assigneeUid,
            assigneeName: dialogResult?.assigneeName,
          }
        : ({} as HistoryInitDto);

      const { state: apmState, assignee } = await this.approvalMatrixService
        .approveFlow(apm.approvalHistoryUid, payload)
        .toPromise();

      const currentStep = apmState.currentStep;
      const newApm = {
        ...apm,
        currentStep,
        nextApproverRoles: currentStep?.info?.roles,
        nextSteps: apmState.nextSteps,
        nextApprover:
          apmState.nextSteps.length > 0
            ? apmState.nextSteps[0].info?.roles.map((r) => r.role).join(' /')
            : '',
        assignee,
      };

      this.actionSubject.next({ action, apm: newApm, state, timestamp });
      return true;
    } catch (err) {
      this.notificationService.error(err.error?.message || err.message);
      return true;
    }
  }
}
