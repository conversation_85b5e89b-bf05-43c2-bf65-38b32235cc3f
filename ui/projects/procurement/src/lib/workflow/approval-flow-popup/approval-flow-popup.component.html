<jb-dialog
  [dialogContent]="content"
  [dialogVisible]="modalOpen"
  (dialogVisibleChange)="cancel()"
>
  <div jb-dialog-body class="dialog-body">
    <form [formGroup]="sendForApprovalForm">
        <prc-form-label label="Approval Flow">
          <jb-single-select-dropdown
            appendTo="body"
            [content]="approvalFlowDropDown"
            [itemTemplate]="itemTemplate"
            [disabled]="isHistory"
            (selectedSubOperation)="onApprovalFlowChange($event)"
          >
          </jb-single-select-dropdown>
        </prc-form-label>

        <prc-form-label class="form-label" label="Next Approver">
          <div *ngIf="nextApprover">
            {{nextApprover}}
          </div>
        </prc-form-label>
        
        <prc-form-label label="Assignee">
          <jb-single-select-dropdown
            appendTo="body"
            [content]="assigneeDropDown"
          >
          </jb-single-select-dropdown>
        </prc-form-label>

        <prc-form-label label="Remarks">
          <jb-textarea
            [content]="remarksTxtArea"
            [autoResize]="false"
          ></jb-textarea>
        </prc-form-label>
    </form>
  </div>
  <jb-dialog-footer
    jb-dialog-footer
    cancelBtnLabel="Cancel"
    okBtnLabel="Confirm"
    [isOkBtnDisabled]="!(isHistory || approvalFlowDropDown.selectedValue)"
    (cancel)="cancel()"
    (ok)="confirm()"
  >
  </jb-dialog-footer>
</jb-dialog>

<ng-template #itemTemplate let-obj>
  <span class="jb-ms-custom-label">
    <div [pTooltip]="obj.label">
      {{ obj.label }}
    </div>
    <div
      *ngIf="approvalFlowApproversMap.get(obj.value) | async as approvers"
      class="custom-template"
      [pTooltip]="approvers"
    >
      {{ approvers }}
    </div>
  </span>
</ng-template>
