import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import {
  RolesPipe,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { IJbDialog, IJbTextArea } from 'jibe-components';
import { approvalFlowDropdown, assigneeDropdown } from './dropdown';
import { ApprovalCommandParams, SendApprovalFlowDto } from './types';

import { ApprovalFlowApproverDto } from '@j3-approval-matrix/dtos/approval-flow';
import { takeUntil } from 'rxjs/operators';
import { ApmDialogService } from '../services/apm-dialog.service';

@Component({
  selector: 'prc-approval-flow-popup',
  templateUrl: './approval-flow-popup.component.html',
  styleUrls: ['./approval-flow-popup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ApprovalFlowPopupComponent extends UnsubscribeComponent {
  public remarksTxtArea: IJbTextArea = {
    id: 'remarks',
    maxlength: 1000,
    rows: 4,
    cols: 4,
    tooltipValue: 'Text Range: Minimum: 0 Maximum: 1000',
    placeholder: 'Add your comments here',
  };

  public sendForApprovalForm: TypedFormGroup<SendApprovalFlowDto>;

  public nextApprover: string;
  public isHistory = false;

  constructor(
    private apmDialogService: ApmDialogService,
    private cdr: ChangeDetectorRef,
    private rolePipe: RolesPipe,
    private readonly fb: FormBuilder
  ) {
    super();
    this.sendForApprovalForm = this.fb.group<SendApprovalFlowDto>({
      assignee: [null, Validators.required],
      remarks: [null, Validators.required],
      approvalFlow: [null, Validators.required],
      nextapprover: [],
    });

    this.apmDialogService.openDialogSubject
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(({ flowList, command, reject, isHistory }) => {
        this.modalOpen = true;
        this.command = command;

        this.reject = reject;
        this.isHistory = isHistory;

        const selectedFlow = flowList.length === 1 ? flowList[0] : null;
        this.approvalFlowDropDown = {
          ...this.approvalFlowDropDown,
          dataSource: flowList,
          selectedValue: selectedFlow?.uid,
        };

        this.approvalFlowApproversMap = new Map(
          flowList.map((flow) => [
            flow.uid,
            this.rolesConversion(flow.approvers),
          ])
        );

        this.nextApprover = selectedFlow?.nextApprover;

        const assignees = selectedFlow?.assignees ?? [];

        this.assigneeDropDown = {
          ...this.assigneeDropDown,
          dataSource: assignees,
          selectedValue: assignees.length === 1 ? assignees[0].uid : null,
        };

        this.cdr.markForCheck();
      });
  }

  public modalOpen: boolean;
  public approvalFlowDropDown = approvalFlowDropdown;
  public approvalFlowApproversMap: Map<string, Promise<string>>;
  public assigneeDropDown = assigneeDropdown;

  public content: IJbDialog = {
    dialogHeader: 'Select Approval Flow',
    dialogWidth: 500,
    showHeader: true,
    closableIcon: true,
  };

  public reject: () => void = () => {};
  public command: (params: ApprovalCommandParams) => void = () => {};

  private closeModal(): void {
    this.modalOpen = false;
  }
  public confirm(): void {
    const { remarks } = this.sendForApprovalForm.value;

    const { selectedValue: assigneeUid, selectedLabel: assigneeName } =
      this.assigneeDropDown;
    this.command({
      approvalFlowUid: this.approvalFlowDropDown.selectedValue,
      assigneeUid,
      assigneeName,
      remarks,
    });
    this.closeModal();
  }
  public cancel(): void {
    this.reject();
    this.closeModal();
  }

  async onApprovalFlowChange(selectedFlowUid: string): Promise<void> {
    const approvalFlow = this.approvalFlowDropDown.dataSource.find(
      (flow) => flow.uid === selectedFlowUid
    );
    this.nextApprover = approvalFlow?.nextApprover;

    this.assigneeDropDown = {
      ...this.assigneeDropDown,
      dataSource: approvalFlow?.assignees?.map((user) => ({
        uid: user.uid,
        name: user.name,
      })),
    };

    this.assigneeDropDown.selectedValue =
      this.assigneeDropDown.dataSource?.length === 1
        ? this.assigneeDropDown.dataSource[0]?.uid
        : null;

    this.cdr.markForCheck();
  }

  private async rolesConversion(
    approvers: ApprovalFlowApproverDto[]
  ): Promise<string> {
    const approversRoles = await Promise.all(
      approvers?.map((approver) => {
        return approver?.roles?.length
          ? this.rolePipe.transform(approver.roles).toPromise()
          : '';
      })
    );
    return approversRoles.join('>');
  }
}
