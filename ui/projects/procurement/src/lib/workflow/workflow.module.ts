import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { JibeComponentsModule } from 'jibe-components';
import { SharedModule } from '../shared/shared.module';
import { ApprovalFlowPopupComponent } from './approval-flow-popup/approval-flow-popup.component';
import { ApmDialogService } from './services/apm-dialog.service';
import { ApprovalMatrixService } from './services/approval-matrix.service';
import { WorkflowApmBtnService } from './services/workflow-apm-btn.service';
import { WorkflowBtnService } from './services/workflow-btn.service';
import { WorkflowService } from './services/workflow.service';

const components = [ApprovalFlowPopupComponent];

@NgModule({
  declarations: components,
  exports: components,
  imports: [
    CommonModule,
    JibeComponentsModule,
    ReactiveFormsModule,
    SharedModule,
  ],
  providers: [
    ApmDialogService,
    ApprovalMatrixService,
    WorkflowService,
    WorkflowApmBtnService,
    WorkflowBtnService,
  ],
})
export class WorkflowModule {}
