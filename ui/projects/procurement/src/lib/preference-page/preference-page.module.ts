import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { EmailConfigPreferenceComponent } from './email-config-preference/email-config-preference.component';
import { PreferencePageComponent } from './preference-page.component';
import { preferencePageRoutes } from './preference-page.routes';
import { PreferenceService } from './services/preference.service';

@NgModule({
  declarations: [PreferencePageComponent, EmailConfigPreferenceComponent],
  imports: [
    CommonModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild(preferencePageRoutes),
  ],
  providers: [PreferenceService],
})
export class PreferencePageModule {}
