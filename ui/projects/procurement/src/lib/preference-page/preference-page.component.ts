import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { PreferencesDto } from '@j3-procurement/dtos/dist/preferences';
import {
  NotificationService,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import { BehaviorSubject } from 'rxjs';
import { eApiResponseType } from '../models/enums/prc-api-response-type.enum';
import {
  ePrcErrorMessages,
  ePrcSuccessMessages,
} from '../models/enums/prc-messages.enum';
import { SidebarMenuService } from '../services/sidebar-menu/sidebar-menu.service';
import {
  defaultSectionEditMode,
  SectionEditMode,
  SectionKey,
} from './section-edit-mode';
import { PreferenceService } from './services/preference.service';
import { Section, sidebarMenu, View } from './sidebar-menu';

@Component({
  selector: 'prc-preference-page',
  templateUrl: './preference-page.component.html',
  styleUrls: ['./preference-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PreferencePageComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public currentView: View = 'procurement';
  public preferencesForm: TypedFormGroup<PreferencesDto>;
  public sectionEditMode: SectionEditMode = {
    ...defaultSectionEditMode,
  };

  private isSaving$ = new BehaviorSubject<boolean>(false);

  constructor(
    private readonly formBuilder: FormBuilder,
    private readonly preferenceService: PreferenceService,
    private readonly sidebarMenuService: SidebarMenuService<View, Section>,
    private readonly notificationService: NotificationService
  ) {
    super();
    this.preferencesForm = this.formBuilder.group<PreferencesDto>({
      sendRfqEmails: [null, [Validators.required]],
    });
  }

  async ngOnInit(): Promise<void> {
    this.sidebarMenuService.init(sidebarMenu, this.componentDestroyed$);
    this.sectionEditMode = { ...defaultSectionEditMode };

    await this.loadPreferences();
  }

  private async loadPreferences(): Promise<PreferencesDto> {
    const result = await this.preferenceService.getPreference();
    if (!result) {
      return;
    }
    this.preferencesForm.patchValue(result);
  }

  public async save(): Promise<void> {
    if (this.preferencesForm.invalid) {
      return this.notificationService.error(ePrcErrorMessages.MandatoryFields);
    }

    this.isSaving$.next(true);
    try {
      await this.preferenceService.save(this.preferencesForm.value);
      this.notificationService.success(ePrcSuccessMessages.RecordSavedSuccess);
    } catch (err) {
      const { message, name } = err.error ?? {};
      this.notificationService.error(
        name === eApiResponseType.OptimisticLockError
          ? ePrcErrorMessages.OptimisticLockError
          : message
      );
    } finally {
      this.isSaving$.next(false);
    }
  }

  public setSectionEditMode(key: SectionKey): void {
    this.sectionEditMode = {
      ...defaultSectionEditMode,
      [key]: true,
    };
  }
}
