import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { ControlContainer } from '@angular/forms';
import { PreferencesDto } from '@j3-procurement/dtos/dist/preferences';
import { TypedFormGroup, UnsubscribeComponent } from 'j3-prc-components';
import { ISingleSelectDropdown, JbControlOutputService } from 'jibe-components';
import { takeUntil } from 'rxjs/operators';
import { rfqEmailDropdown } from './dropdown';

@Component({
  selector: 'prc-email-config-preference',
  templateUrl: './email-config-preference.component.html',
  styleUrls: ['./email-config-preference.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EmailConfigPreferenceComponent
  extends UnsubscribeComponent
  implements OnInit
{
  public tooltip =
    'Setting to control whether it is mandatory for RFQ emails to be generated when sending or resending RFQs from a requisition.';
  public dropdownContent: ISingleSelectDropdown = rfqEmailDropdown;
  public form: TypedFormGroup<PreferencesDto>;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private controlContainer: ControlContainer,
    private readonly jbControlService: JbControlOutputService
  ) {
    super();
  }

  ngOnInit(): void {
    this.form = this.controlContainer.control as TypedFormGroup<PreferencesDto>;
    const ctrl = this.form.get('sendRfqEmails');
    if (!ctrl) {
      return;
    }

    this.updateRfqEmailDropdown({ selectedValue: ctrl.value });

    ctrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((value) => {
        this.updateRfqEmailDropdown({ selectedValue: value });
        this.cdr.markForCheck();
      });

    this.jbControlService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(({ id, selectedValue }) => {
        if (id !== 'sendRfqEmails') {
          return;
        }

        ctrl.patchValue(selectedValue, { emitEvent: true });
        ctrl.markAsDirty();
      });
  }

  private updateRfqEmailDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.dropdownContent = {
      ...this.dropdownContent,
      ...config,
    };
  }
}
