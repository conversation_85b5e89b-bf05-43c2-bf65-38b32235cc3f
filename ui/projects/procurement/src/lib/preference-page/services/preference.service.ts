import { Injectable } from '@angular/core';
import {
  PreferenceKey,
  PreferencesDto,
} from '@j3-procurement/dtos/dist/preferences';
import { ApiRequestService, eCrud, WebApiRequest } from 'jibe-components';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';

@Injectable()
export class PreferenceService {
  constructor(private apiRequestService: ApiRequestService) {}

  save(dto: PreferencesDto): Promise<PreferencesDto> {
    const request: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Preferences,
      action: '',
      crud: eCrud.Put,
      body: dto,
    };

    return this.apiRequestService.sendApiReq(request).toPromise();
  }

  getPreference(key?: PreferenceKey): Promise<PreferencesDto> {
    const request: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Preferences,
      action: key,
      crud: eCrud.Get,
    };

    return this.apiRequestService.sendApiReq(request).toPromise();
  }
}
