import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { CoreModule } from 'j3-prc-components';
import { JibeComponentsModule, JiBeTheme } from 'jibe-components';
import { appConfig, APP_CONFIG } from './app.config';
import { BootstrapComponent } from './bootstrap/bootstrap.component';
import { DeliveryMainPageModule } from './delivery-main-page/delivery-main-page.module';
import { DirectPoSinglePageModule } from './direct-po-single-page/direct-po-single-page.module';
import { GeneralBrowsingPageModule } from './general-browsing-page/general-browsing-page.module';
import { ItemListMainPageModule } from './item-list-main-page/item-list-main-page.module';
import { ItemListSinglePageModule } from './item-list-single-page/item-list-single-page.module';
import { MainFlowPageModule } from './main-flow-page/main-flow-page.module';
import { PoMainPageModule } from './po-main-page/po-main-page.module';
import { PoSinglePageModule } from './po-single-page/po-single-page.module';
import { PrcMainPageModule } from './prc-main-page/prc-main-page.module';
import { PreferencePageModule } from './preference-page/preference-page.module';
import { ProcurementRoutingModule } from './procurement-routing.module';
import { RequisitionMainPageModule } from './requisition-main-page/requisition-main-page.module';
import { RequisitionSinglePageModule } from './requisition-single-page/requisition-single-page.module';
import { FeedDiscussionService } from './services/feed-discussion.service';
import { SidebarMenuService } from './services/sidebar-menu/sidebar-menu.service';

export function winEnv(): unknown {
  return {
    ...window['environment'],
    origin: window['location']['origin'] + '/',
  };
}

const envConfig = { environment: winEnv, theme: JiBeTheme.Figma };

@NgModule({
  declarations: [BootstrapComponent],
  imports: [
    CommonModule,
    CoreModule,
    DeliveryMainPageModule,
    DirectPoSinglePageModule,
    GeneralBrowsingPageModule,
    ItemListMainPageModule,
    ItemListSinglePageModule,
    JibeComponentsModule.forRoot(envConfig),
    MainFlowPageModule,
    PoMainPageModule,
    PoSinglePageModule,
    PrcMainPageModule,
    ProcurementRoutingModule,
    RequisitionMainPageModule,
    RequisitionSinglePageModule,
    PreferencePageModule,
  ],
  providers: [
    { provide: APP_CONFIG, useValue: appConfig },
    FeedDiscussionService,
    SidebarMenuService,
  ],
  exports: [PrcMainPageModule],
})
export class ProcurementModule {}
