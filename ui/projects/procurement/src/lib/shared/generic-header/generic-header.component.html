<div class="statusbar">
  <div
    *ngIf="status"
    class="statusbar-bar"
    [style.backgroundColor]="status?.color"
  ></div>
</div>
<div class="image">
  <ng-container
    *ngIf="uploadImageTemplateRef"
    [ngTemplateOutlet]="uploadImageTemplateRef"
    [ngTemplateOutletContext]="{
      enableUploading: editMode && !titleDisabled,
      fileSelected: fileSelected
    }"
  >
  </ng-container>
</div>
<div class="breadcrumbs" *ngIf="showBreadcrumb">
  <ng-container
    *ngFor="let section of sections"
    [ngTemplateOutlet]="sectionTemplateRef || defaultSectionTemplate"
    [ngTemplateOutletContext]="{
      label: section.label,
      class: section.class,
      labelColor: section.labelColor,
      iconClass: section.iconClass,
      iconColor: section.iconColor,
      command: section.command
    }"
  ></ng-container>
  <div class="status" [style.color]="status?.color">
    <div class="status-dot" [style.background-color]="status?.color"></div>
    <div>{{ status?.text }}</div>
  </div>
</div>
<div class="title">
  <input
    class="jb-text no-background"
    [readonly]="!editMode"
    type="text"
    pInputText
    maxlength="200"
    [disabled]="titleDisabled"
    [ngModel]="title"
    (ngModelChange)="updateTitle($event)"
  />
</div>
<div class="content">
  <ng-content></ng-content>
  <div
    class="icons"
    *ngIf="icons?.ihm || icons?.dangerousGoods || icons?.criticality"
  >
    <div class="icon" *ngIf="icons?.criticality">
      <prc-form-label class="icon-header">Criticality</prc-form-label>
      <i class="icons8-copyright critical-icon"></i>
    </div>
    <div class="icon" *ngIf="icons?.ihm">
      <prc-form-label class="icon-header">IHM</prc-form-label>
      <i class="icons8-high-priority-3 info-icon"></i>
    </div>
    <div class="icon" *ngIf="icons?.dangerousGoods">
      <prc-form-label class="icon-header">Dangerous Goods</prc-form-label>
      <img class="dangerous-goods-icon" src="assets/images/DG-icon.svg" />
    </div>
  </div>
</div>
<div *ngIf="showThreeDot" class="settings" (click)="$event.stopPropagation()">
  <div
    class="icon8-MenuActions three-dots"
    (click)="settingsOptions?.length && settingsMenu.toggle($event)"
  >
    <i class="icons8-menu-2-filled header"></i>
  </div>
  <p-menu
    #settingsMenu
    [model]="settingsOptions"
    [popup]="true"
    appendTo="body"
  ></p-menu>
</div>
<div class="button" (click)="$event.stopPropagation()">
  <ng-container
    *ngFor="let button of buttons"
    [ngTemplateOutlet]="buttonTemplateRef || defaultButtonTemplate"
    [ngTemplateOutletContext]="{
      type: button.buttonType,
      command: button.command,
      label: button.label,
      class: button.buttonClass,
      disabled: button.disabled || readonly,
      tooltip: button.tooltip
    }"
  ></ng-container>
</div>

<ng-template
  #defaultSectionTemplate
  let-label="label"
  let-class="class"
  let-labelColor="labelColor"
  let-iconClass="iconClass"
  let-iconColor="iconColor"
>
  <div class="segment" [ngClass]="class" [ngStyle]="{ color: labelColor }">
    <div class="segment-icon">
      <i [ngClass]="iconClass" [ngStyle]="{ color: iconColor }"></i>
    </div>
    <div>{{ label }}</div>
  </div>
</ng-template>

<ng-template
  #defaultButtonTemplate
  let-type="type"
  let-command="command"
  let-label="label"
  let-disabled="disabled"
  let-class="class"
  let-tooltip="tooltip"
>
  <jb-button
    [type]="type"
    (click)="command()"
    [label]="label"
    [ngClass]="class"
    [disabled]="disabled"
    [pTooltip]="tooltip"
  ></jb-button>
</ng-template>
