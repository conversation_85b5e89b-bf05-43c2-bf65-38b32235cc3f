import {
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
  Optional,
} from '@angular/core';
import { IdLabel, Label, PoTypeLabel } from '@j3-procurement/dtos/label';
import { TypedFormGroup, UnsubscribeComponent } from 'j3-prc-components';
import {
  IMultiSelectDropdown,
  ISingleSelectDropdown,
  JbControlOutputService,
} from 'jibe-components';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { filter, startWith, takeUntil } from 'rxjs/operators';
import {
  assignedToTypeDropdown,
  poTypesDropdown,
  urgencyDropdown,
} from '../../requisition-single-page/sidebar-menu';
import {
  extractDropdownData,
  getDropdownDataFromLabel,
} from '../../utils/label-utils';

import { ControlContainer } from '@angular/forms';
import { EnvCode } from '@j3-procurement/dtos';
import { EcStatus } from '@j3-procurement/dtos/export-control';
import { PoDto } from '@j3-procurement/dtos/po';
import { RequisitionDetailsDto } from '@j3-procurement/dtos/requisition';
import { VesselDetailsDto } from '@j3-procurement/dtos/vessel';
import { PermissionService } from '../../services/permission/permission.service';
import { PoTypeService } from '../../services/po/po-type.service';
import { POService } from '../../services/po/po.service';
import { VesselService } from '../../services/vessel/vessel.service';

import { ApprovalMatrixDetails } from '../../workflow/services/types';
import { WorkflowApmBtnService } from '../../workflow/services/workflow-apm-btn.service';
import { Permission } from './types';
import { createCondition } from './utils';

// TODO: refactor component (get rid of approvalFlowPropsChanges)
@Component({
  selector: 'prc-single-page-sub-header',
  templateUrl: './single-page-sub-header.component.html',
  styleUrls: ['./single-page-sub-header.component.scss'],
})
export class SinglePageSubHeaderComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() approvalMatrixDetails: ApprovalMatrixDetails;
  @Input() isDisable: string;
  @Input() isEditMode = false;
  @Input() readonly = false;
  @Input() reviewMode: boolean;
  @Input() poEditGeneralInfo: boolean;

  @Input() set parentCatalogUids(value: string[]) {
    this.parentCatalogUids$.next(value);
  }
  @Input() set glAccountUids(value: string[]) {
    this.glAccountUids$.next(value);
  }
  @Input() set costValue(value: number) {
    this.costValue$.next(value);
  }
  @Input() set permission(value: Permission) {
    this.permission$.next(value);
  }

  @Input() set dropdownDetails(value: RequisitionDetailsDto | PoDto) {
    if (!value) {
      return;
    }
    this.dropdownDetails$.next(value);
    if (this.headerDetails?.ecStatus != value?.ecStatus) {
      this.ecStatus = value.ecStatus;
    }
    this.headerDetails = value;
    this.loadVesselDetails(value.vessel?.uid);
    this.setAssignedToDropdown(value?.vesselId);
    this.bindDropdownDetails(this.headerDetails);
    this.envCode = this.dropdownDetails?.envCode;
  }

  createDate: Date;
  disablePoType = false;
  ecStatus: EcStatus;
  form: TypedFormGroup<RequisitionDetailsDto>;
  isEditAccess: boolean;
  isPo = false;
  isRequisition = false;
  isVesselAccess: boolean;
  numberOfItems: number | boolean;
  poDate: Date;

  public headerDetails: RequisitionDetailsDto | PoDto = null;
  public assignedToTypeDropdown: ISingleSelectDropdown = assignedToTypeDropdown;
  public poTypesDropdown: ISingleSelectDropdown = poTypesDropdown;
  public urgencyTypeDropdown: ISingleSelectDropdown = urgencyDropdown;
  public permission$ = new BehaviorSubject<Permission>(null);

  private envCode: EnvCode;
  private costValue$ = new Subject<number>();
  private dropdownDetails$ = new Subject<RequisitionDetailsDto | PoDto>();
  private glAccountUids$ = new Subject<string[]>();
  private parentCatalogUids$ = new Subject<string[]>();
  private vesselDetails$ = new Subject<VesselDetailsDto>();

  constructor(
    private controlContainer: ControlContainer,
    private jbControlService: JbControlOutputService,
    private readonly cdr: ChangeDetectorRef,
    private readonly permissionService: PermissionService,
    private readonly poService: POService,
    private readonly poTypeService: PoTypeService,
    private readonly vesselService: VesselService,
    @Optional() private workflowApmBtnService: WorkflowApmBtnService
  ) {
    super();
    this.form = this.controlContainer
      .control as TypedFormGroup<RequisitionDetailsDto>;
    this.setPoTypeDropdown();
    this.approvalFlowPropsChanges();
  }

  async ngOnInit(): Promise<void> {
    if (this.poTypesDropdown && this.headerDetails) {
      this.bindDropdownDetails(this.headerDetails);
    }
    this.isEditAccess =
      this.envCode === 'V' ? this.isVesselAccess : !this.isVesselAccess;

    if (this.isDisable === 'purchaseOrder') {
      this.isPo = true;
    } else {
      this.isRequisition = true;
    }

    this.form = this.controlContainer
      .control as TypedFormGroup<RequisitionDetailsDto>;

    const urgencyCtrl = this.form.get('urgency');
    const assignedToCtrl = this.form.get('assignee');
    const poTypeCtrl = this.form.get('poType');
    const totalItemsCtrl = this.form.get('totalItems');

    assignedToCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((label) => {
        this.updateAssignedDropdown({ selectedValue: label?.uid });
        this.cdr.markForCheck();
      });

    poTypeCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((label) => {
        this.updatePOTypeDropdown({
          selectedValue: label?.uid,
          selectedLabel: label?.name,
        });
        this.cdr.markForCheck();
      });

    totalItemsCtrl?.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((value: number) => {
        this.numberOfItems = value;
        this.cdr.markForCheck();
      });

    urgencyCtrl.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((label) => {
        this.updateUrgencyDropdown({ selectedValue: label?.uid });
        this.cdr.markForCheck();
      });

    this.jbControlService.dynamicControl
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(
        (controlData: IMultiSelectDropdown | ISingleSelectDropdown) => {
          if (!controlData?.id) {
            return;
          }
          const { dataSource, selectedValue } =
            controlData as ISingleSelectDropdown;

          const [controlName, value] = extractDropdownData(controlData);
          if (controlName === 'deliveryPort') {
            const record = dataSource.find(
              (ds) => ds.PORT_ID === selectedValue
            );
            (value as IdLabel).country = record.port_country;
          }

          if (controlName === 'poType') {
            const record = dataSource.find((ds) => ds.uid === selectedValue);
            (value as PoTypeLabel).type = record.types;
          }

          const ctrl = this.form.get(
            controlName as keyof RequisitionDetailsDto
          );
          if (!ctrl) {
            return;
          }

          ctrl.patchValue(value, {
            emitEvent: controlName === 'poType' || controlName === 'department',
          });
          ctrl.markAsDirty();
        }
      );
  }

  private async getPoTypeList(vesselUid: string): Promise<Label[]> {
    const poTypeFilter = await this.poTypeService.getPoTypeFilter(vesselUid);
    return this.poService.getPoTypes(poTypeFilter).toPromise();
  }

  private setPoTypeDropdown(): void {
    combineLatest([this.dropdownDetails$, this.permission$])
      .pipe(filter(([{ poType }, permission]) => Boolean(poType && permission)))
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async ([{ poType, vessel }, permission]) => {
        const { ChangePurchaseType } = permission ?? {};
        const hasPermission =
          ChangePurchaseType &&
          (await this.permissionService.hasPermissions(ChangePurchaseType));

        const poTypeList =
          poTypesDropdown.dataSource ?? (await this.getPoTypeList(vessel?.uid));

        const list = hasPermission
          ? poTypeList
          : poTypeList.filter(({ types }) => types !== 'Direct PO');

        const selectedPoNotExist =
          poType?.uid && !list?.find(({ uid }) => uid === poType.uid);
        this.disablePoType =
          selectedPoNotExist || this.headerDetails.bulkPurchase;

        const dataSource = selectedPoNotExist
          ? [{ uid: poType.uid, po_types: poType.name }]
          : list;

        this.poTypesDropdown = {
          ...this.poTypesDropdown,
          selectedValue: poType?.uid,
          dataSource,
        };

        this.cdr.markForCheck();
      });
  }

  private approvalFlowPropsChanges(): void {
    combineLatest([
      this.dropdownDetails$,
      this.costValue$,
      this.form.get('poType').valueChanges.pipe(startWith({ uid: null })),
      this.vesselDetails$,
      this.parentCatalogUids$,
      this.glAccountUids$,
    ])
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter(
          ([
            details,
            value,
            poType,
            vesselDetails,
            parentCatalogUids,
            glAccountUids,
          ]) =>
            Boolean(
              details &&
                (value || value === 0) &&
                vesselDetails &&
                parentCatalogUids &&
                poType &&
                glAccountUids
            )
        )
      )
      .subscribe(
        ([
          details,
          value,
          poType,
          vesselDetails,
          parentCatalogUids,
          glAccountUids,
        ]) => {
          const {
            vesselManagementCompanyRegistryUid,
            vesselOwnerCompanyRegistryUid,
          } = vesselDetails;
          const poTypeUid = details.poType?.uid || poType.uid;

          const condition = createCondition(
            vesselDetails.vesselId.toString(),
            vesselManagementCompanyRegistryUid,
            vesselOwnerCompanyRegistryUid,
            poTypeUid,
            details.segmentUids,
            parentCatalogUids,
            glAccountUids
          );
          const apmContext = {
            condition,
            value,
            moduleCode: 'procurement',
            functionCode: 'prc_requisition',
            approvalType: 'po',
            vesselId: vesselDetails.vesselId,
            vesselUid: vesselDetails.vesselUid,
          };
          if (this.workflowApmBtnService) {
            this.workflowApmBtnService.apmContext = apmContext;
          }
        }
      );
  }

  private bindDropdownDetails(
    dropdownDetails: RequisitionDetailsDto | PoDto
  ): void {
    const {
      assignee,
      ecStatus: status,
      urgency,
      poType,
      totalItems,
      creationDate,
      poDate,
    } = dropdownDetails;
    this.numberOfItems = totalItems;
    this.ecStatus = status;
    this.createDate = creationDate;
    this.poDate = poDate;
    if (assignee) {
      this.assignedToTypeDropdown = {
        ...this.assignedToTypeDropdown,
        ...getDropdownDataFromLabel(assignee),
      };
    }
    if (urgency) {
      this.urgencyTypeDropdown = {
        ...this.urgencyTypeDropdown,
        ...getDropdownDataFromLabel(urgency),
      };
    }
    if (poType) {
      this.poTypesDropdown = {
        ...this.poTypesDropdown,
        ...getDropdownDataFromLabel(poType),
      };
    }
  }

  private updateAssignedDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.assignedToTypeDropdown = { ...this.assignedToTypeDropdown, ...config };
  }

  private updatePOTypeDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.poTypesDropdown = { ...this.poTypesDropdown, ...config };
  }

  private updateUrgencyDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.urgencyTypeDropdown = { ...this.urgencyTypeDropdown, ...config };
  }

  async loadVesselDetails(vesselUid: string): Promise<void> {
    if (!vesselUid) {
      return;
    }
    const vesselDetails = await this.vesselService
      .getVesselDetailsByUid(vesselUid)
      .toPromise();
    this.vesselDetails$.next(vesselDetails);
  }

  private setAssignedToDropdown(vesselId: number): void {
    if (!vesselId) {
      return;
    }
    this.assignedToTypeDropdown.apiRequest = {
      ...this.assignedToTypeDropdown.apiRequest,
      params: `VesselId=${vesselId}`,
    };
  }
}
