import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { ControlContainer, Validators } from '@angular/forms';
import { scopeType, supplierType } from '@j3-procurement/dtos';

import { stringsEqual } from '@j3-procurement/dtos';
import { Label } from '@j3-procurement/dtos/label';
import { DeliveryMethod } from '@j3-procurement/dtos/po';
import { SupplierAddressDto } from '@j3-procurement/dtos/supplier';
import {
  getDataSourceReq,
  transformDateFormat,
  TypedFormGroup,
  UnsubscribeComponent,
} from 'j3-prc-components';
import {
  CentralizedDataService,
  eDateFormats,
  eUserStatus,
  IJbTextArea,
  IMultiSelectDropdown,
  ISingleSelectDropdown,
  JbControlOutputService,
  WebApiRequest,
} from 'jibe-components';
import f from 'odata-filter-builder';
import { filter, pairwise, startWith, takeUntil, tap } from 'rxjs/operators';

import { POService } from '../../services/po/po.service';
import { SupplierService } from '../../services/supplier/supplier.service';
import {
  extractDropdownData,
  getDropdownControlName,
} from '../../utils/label-utils';
import {
  DeliveryMethodForm,
  DeliveryMethodFormKey,
  linkedControls,
} from './delivery-method-form';
import {
  getAddressDropdown,
  getDeliveryMethodDropdown,
  getRemarkTextArea,
  getSupplierDropdown,
} from './delivery-method-form-dropdowns';

@Component({
  selector: 'prc-delivery-method-form',
  templateUrl: './delivery-method-form.component.html',
  styleUrls: ['./delivery-method-form.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeliveryMethodFormComponent
  extends UnsubscribeComponent
  implements OnInit
{
  @Input() forceShowTwoColumns: boolean;
  @Input() readonly: boolean;

  public addressDropdown: ISingleSelectDropdown = getAddressDropdown();
  public deliveryMethod: DeliveryMethod;
  public deliveryMethodDropdown: ISingleSelectDropdown;
  public deliveryMethodForm: TypedFormGroup<DeliveryMethodForm>;
  public remarkTextArea: IJbTextArea = getRemarkTextArea();
  public supplierDropdown: ISingleSelectDropdown = getSupplierDropdown();
  public supplierLabels: Partial<Record<DeliveryMethod, string>> = {
    'Supplier Consolidation': 'Supplier',
    'Management Company': 'Management Company',
    Warehouse: 'Logistic Company',
  };
  public userDateFormat: string;

  private deliveryMethodNameTypeAndScopeMap = new Map<
    DeliveryMethod,
    {
      type: Extract<supplierType, 'Supplier' | 'All' | 'Management Company'>;
      scope: Extract<scopeType, 'All' | 'Logistics'>;
    }
  >([
    ['Warehouse', { type: 'All', scope: 'Logistics' }],
    ['Management Company', { type: 'Management Company', scope: 'All' }],
    ['Supplier Consolidation', { type: 'Supplier', scope: 'All' }],
  ]);

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly cds: CentralizedDataService,
    private readonly controlContainer: ControlContainer,
    private readonly jbControlService: JbControlOutputService,
    private readonly poService: POService,
    private readonly supplierService: SupplierService
  ) {
    super();

    const request = getDataSourceReq('lib_delivery_method', {
      filter: new f().eq(eUserStatus.ActiveStatus, true),
    });
    this.deliveryMethodDropdown = getDeliveryMethodDropdown(request);
  }

  ngOnInit(): void {
    this.deliveryMethodForm = this.controlContainer
      .control as TypedFormGroup<DeliveryMethodForm>;

    const userDateFormat = this.cds.userDetails.Date_Format?.toUpperCase();
    this.userDateFormat = transformDateFormat(
      userDateFormat ?? eDateFormats.DefaultFormat
    );

    this.bindDropdownDetails();
    this.setSubscriptions();
  }

  private async bindDropdownDetails(): Promise<void> {
    const {
      addressName,
      addressUid,
      deliveryMethod,
      deliveryMethodUid,
      supplierName,
      supplierUid,
    } = this.deliveryMethodForm.controls;

    const addreses = await this.getAddresses(supplierUid.value);

    this.updateAddressDropdown({
      selectedLabel: addressName.value,
      selectedValue: addressUid.value,
      dataSource: addreses,
    });

    const deliveryMethodName = deliveryMethod.value;
    this.deliveryMethod = deliveryMethodName;
    this.updateDeliveryMethodDropdown({
      selectedLabel: deliveryMethodName,
      selectedValue: deliveryMethodUid.value,
    });

    this.updateSupplierDropdown({
      selectedLabel: supplierName.value,
      selectedValue: supplierUid.value,
      apiRequest: this.getSuppliersRequest(deliveryMethodName),
    });

    this.cdr.markForCheck();
  }

  private async getAddresses(
    supplierUid: string
  ): Promise<SupplierAddressDto[]> {
    return supplierUid
      ? await this.supplierService.getAddresses(supplierUid)
      : [];
  }

  private getSuppliersRequest(method: DeliveryMethod): WebApiRequest {
    const typeAndScope = this.deliveryMethodNameTypeAndScopeMap.get(method);
    return (
      typeAndScope &&
      this.poService.getSuppliersReq(typeAndScope.type, typeAndScope.scope)
    );
  }

  private async setAddressDropdownValues(supplierUid: string): Promise<void> {
    const addresses = await this.getAddresses(supplierUid);
    const { addressName, addressUid } = this.deliveryMethodForm.controls;

    this.updateAddressDropdown({ dataSource: addresses });
    if (
      addresses.length === 1 &&
      !stringsEqual(addresses[0].uid, addressUid.value)
    ) {
      const [{ address, uid }] = addresses;

      addressName.markAsDirty();
      addressUid.markAsDirty();

      this.deliveryMethodForm.patchValue({
        addressName: address,
        addressUid: uid,
      });
    }

    this.cdr.markForCheck();
  }

  private setAddressValidator(deliveryMethod: DeliveryMethod): void {
    const { addressUid } = this.deliveryMethodForm.controls;

    if (deliveryMethod in this.supplierLabels) {
      addressUid.setValidators(Validators.required);
    } else {
      addressUid.clearValidators();
    }
    addressUid.updateValueAndValidity();
  }

  private async setDescriptionData(supplierUid: string): Promise<void> {
    const agent =
      supplierUid && (await this.supplierService.getAgent(supplierUid));
    this.deliveryMethodForm.controls.remark.patchValue(agent?.agentDetails);
  }

  private setSubscriptions(): void {
    const { controls } = this.deliveryMethodForm;

    this.jbControlService.dynamicControl
      .pipe(
        takeUntil(this.componentDestroyed$),
        filter((dropdown: ISingleSelectDropdown | IMultiSelectDropdown) => {
          const controlName = getDropdownControlName(dropdown);
          return this.deliveryMethodForm.contains(controlName);
        })
      )
      .subscribe((dropdown: ISingleSelectDropdown | IMultiSelectDropdown) => {
        const [controlName, label] = extractDropdownData(dropdown);
        const dropdownName = controlName as DeliveryMethodFormKey;
        const { name, uid } = (label as Label) ?? {};
        const linkedControl = linkedControls[dropdownName];
        console.log(linkedControl, controlName);

        if (linkedControl) {
          controls[linkedControl].markAsDirty();
          controls[linkedControl].patchValue(name);
        }

        controls[dropdownName].markAsDirty();
        controls[dropdownName].patchValue(uid);
      });

    controls.deliveryMethod.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((deliveryMethod) => {
        this.deliveryMethod = deliveryMethod;
        console.log('Delivery Method..');
        console.log(this.deliveryMethod);
        this.setAddressValidator(deliveryMethod);

        if (this.deliveryMethodForm.dirty) {
          controls.supplierUid.markAsDirty();
          controls.supplierName.markAsDirty();

          this.deliveryMethodForm.patchValue({
            supplierUid: undefined,
            supplierName: undefined,
          });
        }

        this.updateSupplierDropdown({
          apiRequest: this.getSuppliersRequest(deliveryMethod),
          dataSource: undefined,
        });
        this.updateDeliveryMethodDropdown({ selectedLabel: deliveryMethod });

        this.cdr.markForCheck();
      });

    controls.deliveryMethodUid.valueChanges
      .pipe(
        startWith(controls.deliveryMethodUid.value),
        tap((value) => {
          console.log(value);
          console.log(this.deliveryMethodDropdown.dataSource);
        }),
        takeUntil(this.componentDestroyed$),
        pairwise(),
        filter(
          ([previousValue, currentValue]) => previousValue !== currentValue
        )
      )
      .subscribe(([, uid]) => {
        this.updateDeliveryMethodDropdown({ selectedValue: uid });

        if (this.deliveryMethodForm.dirty && !uid) {
          this.deliveryMethodForm.reset();
          this.deliveryMethodForm.markAsDirty();
        }

        this.cdr.markForCheck();
      });

    controls.supplierName.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((name: string) => {
        this.updateSupplierDropdown({ selectedLabel: name });

        this.cdr.markForCheck();
      });

    controls.supplierUid.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((uid: string) => {
        this.updateSupplierDropdown({ selectedValue: uid });

        if (this.deliveryMethodForm.dirty) {
          controls.addressUid.markAsDirty();
          controls.addressName.markAsDirty();

          this.deliveryMethodForm.patchValue({
            addressUid: undefined,
            addressName: undefined,
          });

          this.setDescriptionData(uid);
        }

        this.setAddressDropdownValues(uid);

        this.cdr.markForCheck();
      });

    controls.addressUid.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((uid: string) => {
        this.updateAddressDropdown({ selectedValue: uid });

        this.cdr.markForCheck();
      });

    controls.addressName.valueChanges
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((name: string) => {
        this.updateAddressDropdown({ selectedLabel: name });

        this.cdr.markForCheck();
      });
  }

  private updateAddressDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.addressDropdown = { ...this.addressDropdown, ...config };
  }

  private updateDeliveryMethodDropdown(
    config: Partial<ISingleSelectDropdown>
  ): void {
    this.deliveryMethodDropdown = { ...this.deliveryMethodDropdown, ...config };
  }

  private updateSupplierDropdown(config: Partial<ISingleSelectDropdown>): void {
    this.supplierDropdown = { ...this.supplierDropdown, ...config };
  }
}
