<div class="delivery-port-form">
  <p-accordion [activeIndex]="0">
    <p-accordionTab header="Delivery Port">
      <form class="formcontainer" [formGroup]="deliveryPortForm">
        <div [class.two-columns]="forceShowTwoColumns || vesselMovemement">
          <div class="column">
            <prc-form-label
              label="Vessel Movement"
              class="form-label"
              *ngIf="!readonly"
            >
              <jb-single-select-dropdown
                [content]="vesselMovementDropdown"
                appendTo="body"
              ></jb-single-select-dropdown>
            </prc-form-label>

            <ng-container *ngIf="vesselMovemement">
              <prc-form-label
                label="Ports"
                class="form-label"
                *ngIf="vesselMovemement === 'port'"
              >
                <prc-upcoming-port-select
                  *ngIf="!readonly; else readonlyPort"
                  [showLabel]="true"
                  [port]="port"
                  [vesselId]="vesselId"
                  (selectedPortChange)="onUpcomingPortChange($event)"
                ></prc-upcoming-port-select>
              </prc-form-label>

              <prc-form-label label="Vessel ETA" class="form-label">
                <jb-calendar
                  appendTo="body"
                  formControlName="vesselEta"
                  [content]="etaContent"
                  [disabled]="readonly"
                ></jb-calendar>
              </prc-form-label>

              <prc-form-label
                *ngIf="vesselMovemement === 'other'"
                label="Requested Delivery Date"
                class="form-label"
              >
                <ng-template
                  *ngTemplateOutlet="requestedDeliveryDate"
                ></ng-template>
              </prc-form-label>

              <prc-form-label
                *ngIf="vesselMovemement === 'port'"
                label="Agent"
                class="form-label"
              >
                <jb-single-select-dropdown
                  [content]="agentTypeDropdown"
                  [readOnlyValidator]="readonly"
                  [disabled]="deliveryPortForm.controls.agentType.disabled"
                ></jb-single-select-dropdown>
              </prc-form-label>

              <prc-form-label label="Agent Details" class="form-label">
                <jb-textarea
                  [content]="agentDetailsTextArea"
                  [readonly]="readonly"
                ></jb-textarea>
              </prc-form-label>
            </ng-container>
          </div>

          <div class="column">
            <ng-container *ngIf="vesselMovemement">
              <prc-form-label
                *ngIf="vesselMovemement === 'port'"
                label="Requested Delivery Date"
                class="form-label"
              >
                <ng-template
                  *ngTemplateOutlet="requestedDeliveryDate"
                ></ng-template>
              </prc-form-label>

              <prc-form-label
                *ngIf="vesselMovemement === 'other'"
                label="Delivery Port"
                class="form-label"
              >
                <prc-generic-port-select
                  *ngIf="!readonly; else readonlyPort"
                  [showLabel]="true"
                  [portObject]="port"
                  (selectedPortChange)="onDeliveryPortChange($event)"
                ></prc-generic-port-select>
              </prc-form-label>

              <prc-form-label label="Vessel ETD" class="form-label">
                <jb-calendar
                  appendTo="body"
                  formControlName="vesselEtd"
                  [content]="etdContent"
                  [disabled]="readonly"
                ></jb-calendar>
              </prc-form-label>

              <prc-form-label label="Agent Name" class="form-label">
                <prc-supplier-select
                  dialogHeader="Select Agent"
                  formControlName="agentName"
                  placeholder="Add Agent"
                  [apiRequest]="agentListRequest"
                  [readonly]="readonly"
                  (selectedSupplier)="onAgentChange($event)"
                ></prc-supplier-select>
              </prc-form-label>

              <prc-form-label label="Remarks" class="form-label">
                <jb-textarea
                  [content]="remarkTextArea"
                  [readonly]="readonly"
                ></jb-textarea>
              </prc-form-label>
            </ng-container>
          </div>
        </div>

        <ng-template #readonlyPort>
          <input
            autocomplete="off"
            class="jb-text"
            pInputText
            [ngModel]="port | formatPort"
            [ngModelOptions]="{ standalone: true }"
            [readonly]="true"
          />
        </ng-template>

        <ng-template #requestedDeliveryDate>
          <jb-calendar
            appendTo="body"
            formControlName="requestedDeliveryDate"
            [content]="requestedDeliveryContent"
            [disabled]="readonly"
          ></jb-calendar>
        </ng-template>
      </form>
    </p-accordionTab>
  </p-accordion>
</div>
