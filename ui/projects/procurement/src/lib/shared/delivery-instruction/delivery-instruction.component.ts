import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  DeliveryMethod,
  PoDeliveryMethodDto,
  PoDeliveryPortDto,
  VesselMovement,
} from '@j3-procurement/dtos/po';
import { TypedFormGroup, UnsubscribeComponent } from 'j3-prc-components';
import { combineLatest, merge } from 'rxjs';
import { startWith, takeUntil } from 'rxjs/operators';
import {
  DeliveryMethodForm,
  getDeliveryMethodFormConfig,
  mapDeliveryMethodDtoToForm,
} from '../delivery-method-form/delivery-method-form';
import {
  DeliveryPortForm,
  getDeliveryPortFormConfig,
  mapDeliveryPortDtoToForm,
} from '../delivery-port-form/delivery-port-form';

import { FormBuilder } from '@angular/forms';
import { UpdateDeliveryInstructionDto } from '@j3-procurement/dtos/delivery-instruction';
import { SlfPermission } from '@j3-procurement/dtos/slf';
import { AgentDto } from '@j3-procurement/dtos/supplier';
import { PoPermission } from '../../models/enums/po-permission.enum';
import { Port } from '../../models/interfaces/port';
import { DeliveryInstructionService } from '../../services/delivery-instruction/delivery-instruction.service';
import { PermissionService } from '../../services/permission/permission.service';
import { SupplierService } from '../../services/supplier/supplier.service';
import { deliveryMethodsWithDetails } from './constants';

@Component({
  selector: 'prc-delivery-instruction',
  templateUrl: './delivery-instruction.component.html',
  styleUrls: ['./delivery-instruction.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeliveryInstructionComponent
  extends UnsubscribeComponent
  implements OnInit, OnChanges
{
  @Input() objectUid: string;
  @Input() poIssueUid: string;
  @Input() readonly: boolean;
  @Input() slfPermission: SlfPermission;
  @Input() vesselUid: string;

  @Output() deliveryInstructionData =
    new EventEmitter<UpdateDeliveryInstructionDto>();

  public deliveryMethodForm: TypedFormGroup<DeliveryMethodForm>;
  public deliveryPortForm: TypedFormGroup<DeliveryPortForm>;
  public port: Port;
  public forceShowTwoColumns: boolean;
  public canEditDeliveryInstruction?: boolean;

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly fb: FormBuilder,
    private readonly supplierService: SupplierService,
    private readonly deliveryInstructionService: DeliveryInstructionService,
    private readonly permissionService: PermissionService
  ) {
    super();
  }

  async ngOnInit(): Promise<void> {
    const deliveryMethodFormConfig = getDeliveryMethodFormConfig();
    const deliveryPortFormConfig = getDeliveryPortFormConfig();
    this.deliveryMethodForm = this.fb.group(deliveryMethodFormConfig);
    this.deliveryPortForm = this.fb.group(deliveryPortFormConfig);

    const [canEditDeliveryInstruction] = await Promise.all([
      this.permissionService.hasPermissions(
        PoPermission.UpdateDeliveryInstruction
      ),
      this.setFormData(),
    ]);

    this.subscribeFormValueChanges();

    const { movement } = this.deliveryPortForm.controls;
    const { deliveryMethod } = this.deliveryMethodForm.controls;
    combineLatest([
      movement.valueChanges.pipe(startWith(movement.value)),
      deliveryMethod.valueChanges.pipe(startWith(deliveryMethod.value)),
    ])
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(([vesselMovement, deliveryMethodName]) =>
        this.setForceShowTwoColumns(vesselMovement, deliveryMethodName)
      );

    this.canEditDeliveryInstruction =
      this.slfPermission === 'edit' && canEditDeliveryInstruction;

    if (this.readonly || !this.canEditDeliveryInstruction) {
      this.deliveryPortForm.disable();
      this.deliveryMethodForm.disable();
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes?.poIssueUid) {
      this.poIssueUid = changes.poIssueUid.currentValue;
      this.setFormData();
    }
  }

  private subscribeFormValueChanges(): void {
    merge(
      this.deliveryPortForm.valueChanges,
      this.deliveryMethodForm.valueChanges
    )
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.saveDeliveryInstruction();
      });
  }

  public async saveDeliveryInstruction(): Promise<void> {
    const deliveryPortDto = this.mapDeliveryPortFormToDto();
    const deliveryMethodDto = this.mapDeliveryMethodFormToDto();
    const data: UpdateDeliveryInstructionDto = {
      deliveryMethodDto,
      deliveryPortDto,
    };
    this.deliveryInstructionData.emit(data);
  }

  private mapDeliveryMethodFormToDto(): PoDeliveryMethodDto {
    const formValues = this.deliveryMethodForm.getRawValue();
    const { deliveryMethod, deliveryMethodUid } = formValues;

    if (!deliveryMethodUid) {
      return;
    }

    return deliveryMethodsWithDetails.includes(deliveryMethod)
      ? formValues
      : { deliveryMethod, deliveryMethodUid };
  }

  private mapDeliveryPortFormToDto(): PoDeliveryPortDto {
    const deliveryPort = this.deliveryPortForm.getRawValue();
    if (!deliveryPort.movement) {
      return;
    }
    return deliveryPort;
  }

  private async setFormData(): Promise<void> {
    if (!this.objectUid) {
      return;
    }
    const updateDeliveryDetails = await this.deliveryInstructionService
      .getDeliveryInstruction(this.objectUid, this.readonly && this.poIssueUid)
      .toPromise();

    const {
      deliveryPortDetails: deliveryPort,
      deliveryMethodDetails: deliveryMethod,
    } = updateDeliveryDetails ?? {};
    const { agentDetail, agentUid } = deliveryPort ?? {};

    const { agentName: name, agentDetails } = agentUid
      ? await this.supplierService.getAgent(agentUid)
      : ({} as AgentDto);

    const portFormValues = mapDeliveryPortDtoToForm({
      ...deliveryPort,
      agentName: name,
      agentDetail: agentDetail ?? agentDetails,
    });
    this.deliveryPortForm.patchValue(portFormValues);

    if (deliveryPort?.portName) {
      this.port = {
        name: deliveryPort.portName,
        country: deliveryPort.portCountry,
      };
    }

    const methodFormValues = mapDeliveryMethodDtoToForm(deliveryMethod);
    this.deliveryMethodForm.patchValue(methodFormValues);

    this.cdr.markForCheck();
  }

  private setForceShowTwoColumns(
    movement: VesselMovement,
    deliveryMethod: DeliveryMethod
  ): void {
    this.forceShowTwoColumns =
      deliveryMethodsWithDetails.includes(deliveryMethod) || !!movement;
  }
}
