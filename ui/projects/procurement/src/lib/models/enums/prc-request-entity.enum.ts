export enum ePrcRequestEntity {
  AccountMapping = 'prc-catalog/account-mappings',
  ApprovalMatrix = 'approval-matrix/public',
  ApprovalMatrixV2 = 'approval-matrix/v2',
  Browsing = 'prc/browsing',
  Catalogs = 'prc-catalog',
  Certificate = 'prc/certificates',
  Companies = 'prc/companies',
  Contract = 'prc-contracts/contracts',
  Delivery = 'prc/deliveries',
  DeliveryInstruction = 'prc/delivery-instruction',
  FinaceItems = 'prc/finance-items',
  General = 'prc/generals',
  Grid = 'prc/grids',
  Handover = 'prc/handovers',
  Infra = 'Infra',
  Infrastructure = 'infra',
  Inventory = 'inventory',
  Invoice = 'accounting/invoice',
  ItemEc = 'prc/item-export-control',
  ItemList = 'prc/itemlists',
  Items = 'prc-catalog/items',
  Machinery = 'prc/machineries',
  OnboardCrew = 'crew/onboard-crew',
  Po = 'prc/po',
  PoItems = 'prc/po-items',
  Quotation = 'prc/quotations',
  Requisition = 'prc/requisitions',
  Rfq = 'prc/rfqs',
  Sidebar = 'sidebar',
  Preferences = 'prc/preferences',
  Supplier = 'prc/suppliers',
  TaskManager = 'task-manager',
  TaskStatus = 'prc/task-statuses',
  Vessels = 'prc/vessels',
  Workflow = 'prc/workflows',
}
