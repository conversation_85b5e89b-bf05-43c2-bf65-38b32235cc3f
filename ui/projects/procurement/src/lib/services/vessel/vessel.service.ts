import { VesselDetailsDto, VesselFeature } from '@j3-procurement/dtos/vessel';
import {
  ApiRequestService,
  eApiBase,
  eCrud,
  eEntities,
  VesselService as JbVesselService,
  WebApiRequest,
} from 'jibe-components';
import { BehaviorSubject, Observable } from 'rxjs';
import { map, shareReplay, tap } from 'rxjs/operators';

import { Injectable } from '@angular/core';
import f from 'odata-filter-builder';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { Vessel } from '../../models/interfaces/vessel';

@Injectable({ providedIn: 'root' })
export class VesselService {
  private vessels$: Observable<Vessel[]>;
  private vessels = new Map<string, Vessel>();
  private vesselUidSubject = new BehaviorSubject<string>(null);

  public vesselUid$ = this.vesselUidSubject.asObservable();

  get vesselUid(): string {
    return this.vesselUidSubject.value;
  }

  set vesselUid(value: string) {
    this.vesselUidSubject.next(value);
  }

  constructor(
    private readonly apiRequestService: ApiRequestService,
    private readonly jibeVesselService: JbVesselService
  ) {}

  public getVessels(): Observable<Vessel[]> {
    if (!this.vessels$) {
      this.vessels$ = this.jibeVesselService.getVessels().pipe(
        map((jbVessels = []) =>
          jbVessels.map(
            ({
              vessel_uid: uid,
              Vessel_Name: name,
              Vessel_ID: vesselId,
              system_type: systemType,
            }) => ({
              uid,
              name,
              vesselId,
              systemType,
            })
          )
        ),
        tap((vessels = []) => {
          this.vessels = new Map(vessels.map((vessel) => [vessel.uid, vessel]));
          this.vesselUid = this.vesselUid ?? vessels[0]?.uid;
        }),
        shareReplay(1)
      );
    }
    return this.vessels$;
  }

  public getCurrentVessel(): Vessel {
    return this.getVesselByUid(this.vesselUid);
  }

  public getVesselByUid(uid: string): Vessel {
    return this.vessels.get(uid);
  }

  public getVesselDetailsByUid(uid: string): Observable<VesselDetailsDto> {
    const request: WebApiRequest = {
      apiBase: eApiBase.MasterAPI,
      entity: eEntities.Master,
      action: ePrcRequestAction.GetVesselDetails,
      crud: eCrud.Post,
      odata: {
        count: 'false',
        filter: f().eq('vessel_uid', uid),
      },
    };

    return this.apiRequestService.sendApiReq(request).pipe(
      map((records: VesselFeature[]) => {
        const [feat = {} as VesselFeature] = records;
        return {
          /** using a property name in Pascal_Snake_Case as a workaround for #852626 */
          shortName: feat.vessel_short_name ?? (feat as any).Vessel_Short_Name,
          vesselId: feat.vesselId,
          managementCompanyCode: feat.Vessel_Management_Company_Code,
          managementCompanyName: feat.Vessel_Management_Company_Name,
          vesselManagementCompanyRegistryUid:
            feat.Vessel_Management_Company_Company_Registry_UID,
          vesselClass: feat.VesselClass,
          vesselHullNo: feat.Vessel_Hull_No,
          yardNumber: feat.Yard_Number,
          vesselName: feat.vessel,
          imoNo: feat.imo_no,
          vesselOwnerCode: feat.Vessel_Owner_Code,
          vesselOwnerName: feat.Vessel_Owner_Name,
          vesselOwnerCompanyRegistryUid: feat.Vessel_Owner_Company_Registry_UID,
          vesselManagerName: feat.Vessel_Manager_Name,
          vesselManagerEmail: feat.Vessel_Manager_Email,
          vesselManagerPhoneNo: feat.Vessel_Manager_Phone_No,
          vesselFlag: feat.Vessel_Flag_Name,
          vesselUid: feat.vessel_uid,
        };
      })
    );
  }
}
