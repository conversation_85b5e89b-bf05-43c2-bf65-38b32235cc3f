import { Injectable } from '@angular/core';
import { wait } from '@j3-prc-shared/dtos';
import { supplant } from '@j3-procurement/dtos';
import {
  AddTaskStatusesRequestDto,
  SaveTaskStatusDto,
  TaskStatusDto,
  UpdateTaskStatusRoleDto,
} from '@j3-procurement/dtos/task-status';
import { ApiRequestService, eCrud, WebApiRequest } from 'jibe-components';
import { Observable } from 'rxjs';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';

@Injectable({
  providedIn: 'root',
})
export class TaskStatusService {
  constructor(private apiRequestService: ApiRequestService) {}

  public getTaskStatus(objectUid: string): Promise<TaskStatusDto> {
    return this.apiRequestService
      .sendApiReq({
        apiBase: ePrcRequestApiBase.j3ProcurementAPI,
        entity: ePrcRequestEntity.TaskStatus,
        action: objectUid,
        crud: eCrud.Get,
      })
      .toPromise();
  }

  /**
   * @deprecated
   */
  addTasks(body: AddTaskStatusesRequestDto): Observable<void> {
    const updateRequisitionTaskRequest: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.TaskStatus,
      body: body,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(updateRequisitionTaskRequest);
  }
  saveTaskStatus(
    objectUid: string,
    body: SaveTaskStatusDto
  ): Observable<string> {
    const saveRequisitionTaskRequest: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.TaskStatus,
      action: objectUid,
      body: body,
      crud: eCrud.Put,
    };
    return this.apiRequestService.sendApiReq(saveRequisitionTaskRequest);
  }
  updateApproverRole(
    objectUid: string,
    body: UpdateTaskStatusRoleDto
  ): Observable<Date> {
    const request: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.TaskStatus,
      action: supplant(ePrcRequestAction.UpdateTaskStatusRole, { objectUid }),
      body: body,
      crud: eCrud.Put,
    };
    return this.apiRequestService.sendApiReq(request);
  }

  /**
   * Polls the task status until it is no longer inProgress.
   */
  public async waitForDone(objectUid: string): Promise<void> {
    let attempt = 0;
    let task: TaskStatusDto;
    do {
      const delayMs = this.calculateDelay(attempt++);
      await wait(delayMs);
      task = await this.getTaskStatus(objectUid);
      if (!task) {
        throw new Error('Failed to retrieve task status.');
      }
      if (task.error) {
        throw new Error(task.error);
      }
    } while (task.inProgress);
  }

  private calculateDelay(attempt: number): number {
    // for first 2 attempts, wait 500ms
    // for attempts 2 to 11, wait 1000ms
    // for attempt 12 and onwards, wait 2000ms
    return attempt < 2 ? 500 : attempt < 12 ? 1000 : 2000;
  }
}
