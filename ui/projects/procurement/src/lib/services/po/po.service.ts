import {
  PoCancelDto,
  PoCancelResponseDto,
  PoDto,
  POFinancesRequestDto,
  PoPreviewDto,
  PoRaiseDto,
  POSummaryDto,
  PoUpdateStatusDto,
  RaisePoResponseDto,
  UpdatePoAdditionalInfoDto,
  UpdatePoDto,
} from '@j3-procurement/dtos/po';
import {
  ApiRequestService,
  AuthService,
  eApiBase,
  eCrud,
  eEntities,
  OData,
  WebApiRequest,
} from 'jibe-components';
import { Observable } from 'rxjs';

import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { JCDSPortData } from '@j3-prc-shared/dtos/jcds';
import { supplant } from '@j3-procurement/dtos';
import { PoGLAccountBudgetDto } from '@j3-procurement/dtos/budgets';
import { Label } from '@j3-procurement/dtos/label';
import { PoStatusBranchResponseDto } from '@j3-procurement/dtos/po-status-branch';
import { WithSlf } from '@j3-procurement/dtos/slf';
import { getFile } from 'j3-prc-components';
import f from 'odata-filter-builder';
import { map } from 'rxjs/operators';
import { ePrcRequestAction } from '../../models/enums/prc-request-action.enum';
import { ePrcRequestApiBase } from '../../models/enums/prc-request-api-base.enum';
import { ePrcRequestEntity } from '../../models/enums/prc-request-entity.enum';
import { getUrl } from '../../utils/get-url';

export const GET_PORT_DETAILS_REQ: Readonly<WebApiRequest> = Object.freeze({
  apiBase: eApiBase.MasterAPI,
  entity: eEntities.Library,
  action: ePrcRequestAction.GetLibraryDataByCode,
  params: 'libraryCode=port',
  crud: eCrud.Post,
});

@Injectable({ providedIn: 'root' })
export class POService {
  constructor(
    private readonly apiRequestService: ApiRequestService,
    private readonly httpClient: HttpClient
  ) {}

  public cancelPo(
    poUid: string,
    data: PoCancelDto
  ): Observable<PoCancelResponseDto> {
    const cancelPoData: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/cancel`,
      crud: eCrud.Post,
      body: data,
    };
    return this.apiRequestService.sendApiReq(cancelPoData);
  }

  public closePo(poUid: string): Observable<boolean> {
    const closePo: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/closing`,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(closePo);
  }

  getPoBudgets(poUid: string): Observable<PoGLAccountBudgetDto[]> {
    const poBudget: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/budgets`,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(poBudget);
  }

  /**
   * @description: Used to get PO Single Page grid records
   **/
  getPoSinglePageMatrix(requisitionUid: string): WebApiRequest {
    const poSinglePageMatrixReq: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Requisition,
      action: `${requisitionUid}/po/list`,
      crud: eCrud.Post,
    };
    return poSinglePageMatrixReq;
  }

  /**
   * @description: Used to get PO Single Page general information records
   **/
  public getPoInformation(poUid: string): Observable<WithSlf<PoDto>> {
    const getPOInfo: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: poUid,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(getPOInfo);
  }

  getPoStatusBranches(poUid: string): Observable<PoStatusBranchResponseDto> {
    const getPOStatusBranches: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/order-status-branches`,
      crud: eCrud.Get,
    };
    return this.apiRequestService.sendApiReq(getPOStatusBranches);
  }
  public getSuppliersReq(type: string, scope: string): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Supplier,
      crud: eCrud.Get,
      action: `v0/${type}/${scope}`,
    };
  }

  public getJobsRequest(poUid: string): WebApiRequest {
    return {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/jobs/list`,
      crud: eCrud.Post,
    };
  }

  public getPoFinanceSummaryMatrix(
    poUid: string
  ): Observable<POFinancesRequestDto[]> {
    const poFinanceSummary: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/finances-summary/list`,
      crud: eCrud.Post,
    };
    return this.apiRequestService.sendApiReq(poFinanceSummary);
  }

  public getPortsData(odata?: OData): Promise<JCDSPortData[]> {
    const portsReq = {
      ...GET_PORT_DETAILS_REQ,
      odata: odata ?? {
        count: 'false',
        orderby: 'PORT_NAME',
        skip: '0',
        top: '10000',
        filter: new f().eq('active_status', true),
      },
    };

    return this.apiRequestService.sendApiReq(portsReq).toPromise();
  }

  public getPoSummary(poUid: string): Observable<POSummaryDto> {
    return this.apiRequestService.sendApiReq({
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/summary`,
      crud: eCrud.Get,
    });
  }

  public getPoTypes(filter): Observable<Label[]> {
    const poTypesRequest: WebApiRequest = {
      apiBase: eApiBase.MasterAPI,
      entity: eEntities.Library,
      crud: eCrud.Post,
      params: `libraryCode=poType`,
      action: ePrcRequestAction.GetLibraryDataByCode,
      odata: { filter, count: 'false' },
    };
    return this.apiRequestService.sendApiReq(poTypesRequest);
  }

  getUpdateStatusDetails(
    poUid?: string,
    poIssueUid?: string
  ): Observable<PoUpdateStatusDto> {
    const getUpdateStatusDetails: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: supplant(ePrcRequestAction.OrderStatusList, { poUid }),
      crud: eCrud.Post,
      body: { poUid, poIssueUid },
    };
    return this.apiRequestService.sendApiReq(getUpdateStatusDetails);
  }

  public previewPo(poUid: string, dto: PoPreviewDto): Observable<File> {
    const tokenId = AuthService.getTokenId();
    const url = getUrl({
      entity: ePrcRequestEntity.Po,
      action: supplant(ePrcRequestAction.PoPreview, { poUid }),
    });
    return this.httpClient
      .post<File>(url, dto, {
        headers: { Authorization: tokenId },
        observe: 'response',
        responseType: 'blob' as 'json',
      })
      .pipe(map((res) => getFile(res)));
  }

  public saveAdditionalInfo(
    poUid: string,
    data: UpdatePoAdditionalInfoDto
  ): Observable<any> {
    const saveAdditionalInformation: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/order-status`,
      crud: eCrud.Put,
      body: data,
    };
    return this.apiRequestService.sendApiReq(saveAdditionalInformation);
  }

  public saveRaisePoData(
    poUid: string,
    data: PoRaiseDto
  ): Observable<RaisePoResponseDto> {
    const raisePoData: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/raise-po`,
      crud: eCrud.Post,
      body: data,
    };
    return this.apiRequestService.sendApiReq(raisePoData);
  }

  public saveSupplierConfirmation(poUid: string, data: any): Observable<any> {
    const saveSupplierConfirmation: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}/order-status`,
      crud: eCrud.Post,
      body: data,
    };
    return this.apiRequestService.sendApiReq(saveSupplierConfirmation);
  }

  /**
   * @description: Used to save  PO Single Page general information records
   **/
  updatePo(poUid: string, dto: UpdatePoDto): Promise<Date> {
    const updatePO: WebApiRequest = {
      apiBase: ePrcRequestApiBase.j3ProcurementAPI,
      entity: ePrcRequestEntity.Po,
      action: `${poUid}`,
      body: dto,
      crud: eCrud.Put,
    };
    return this.apiRequestService.sendApiReq(updatePO).toPromise();
  }
}
