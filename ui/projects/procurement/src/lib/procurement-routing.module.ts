import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AuthGuardService } from 'jibe-components';

import { BootstrapComponent } from './bootstrap/bootstrap.component';
import { directPoSinglePageRoutes } from './direct-po-single-page/direct-po-single-page.routes';
import { generalBrowsingPageRoutes } from './general-browsing-page/general-browsing-page-routes';
import { ItemListMainPageComponent } from './item-list-main-page/item-list-main-page.component';
import { itemListSinglePageRoutes } from './item-list-single-page/item-list-single-page.routes';
import { prcMainFlowPageRoutes } from './main-flow-page/main-flow-page.routes';
import { ePrcPermission } from './models/enums/prc-permission.enum';
import { ProtectedRoutes } from './models/interfaces/protected-route';
import { poSinglePageRoutes } from './po-single-page/po-single-page.routes';
import { PrcMainPageComponent } from './prc-main-page/prc-main-page.component';
import { RouteConstants } from './prc-main-page/route-constants';
import { preferencePageRoutes } from './preference-page/preference-page.routes';
import { requisitionSinglePageRoutes } from './requisition-single-page/requisition-single-page.routes';
import { CatalogResolver } from './services/catalog/catalog.resolver';
import { PermissionGuard } from './services/permission/permission.guard';
import { VesselResolver } from './services/vessel/vessel.resolver';
import { ViewAccessResolver } from './services/view-access/view-access.resolver';

const routes: ProtectedRoutes = [
  {
    path: '',
    component: BootstrapComponent,
    canActivate: [AuthGuardService],
    children: [
      {
        path: `prc-main-page/:${RouteConstants.componentUid}/:${RouteConstants.vesselUid}`,
        component: PrcMainPageComponent,
        resolve: {
          viewAccess: ViewAccessResolver,
          catalogUids: CatalogResolver,
        },
      },
      {
        path: 'item-list-main-page',
        component: ItemListMainPageComponent,
        canActivate: [PermissionGuard],
        data: {
          permissions: [ePrcPermission.ListView],
        },
        resolve: {
          vessels: VesselResolver,
        },
      },
      ...directPoSinglePageRoutes,
      ...generalBrowsingPageRoutes,
      ...itemListSinglePageRoutes,
      ...poSinglePageRoutes,
      ...preferencePageRoutes,
      ...prcMainFlowPageRoutes,
      ...requisitionSinglePageRoutes,
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ProcurementRoutingModule {}
