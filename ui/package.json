{"name": "@j3-procurement/ui", "version": "1.0.0", "scripts": {"ng": "ng", "build": "ng build", "build:production": "./node_modules/.bin/ng build procurement --prod", "lint": "ng lint frontend-wrapper", "lint:fix": "ng lint --fix frontend-wrapper", "start": "echo Starting... && ng serve", "test": "ng test", "watch": "ng build --watch"}, "private": false, "dependencies": {"@angular/animations": "^9.1.12", "@angular/cdk": "^9.2.4", "@angular/common": "^9.1.12", "@angular/compiler": "^9.1.12", "@angular/core": "^9.1.12", "@angular/elements": "^9.1.12", "@angular/forms": "^9.1.12", "@angular/platform-browser": "^9.1.12", "@angular/platform-browser-dynamic": "^9.1.12", "@angular/router": "^9.1.12", "@fullcalendar/core": "^5.11.3", "@j3-approval-matrix/dtos": "^3.2411.118", "@j3-inventory/delivery": "3.3211.530-rel.2501", "@j3-inventory/shared": "^1.9.0", "@j3-prc-catalog/dtos": "^3.2411.195", "@j3-prc-contracts/dtos": "^3.2411.120", "@j3-prc-shared/dtos": "^3.2411.140", "@j3-procurement/dtos": "^3.2411.281", "@jb-shared-service/jb-shared-dto": "3.251.212", "@syncfusion/ej2-angular-buttons": "^18.4.46", "@syncfusion/ej2-angular-charts": "^18.1.59", "@syncfusion/ej2-angular-image-editor": "20.4.51-ngcc", "@syncfusion/ej2-angular-layouts": "^18.1.52", "@syncfusion/ej2-angular-navigations": "18.4.35", "@syncfusion/ej2-angular-popups": "20.2.38-ngcc", "@syncfusion/ej2-angular-richtexteditor": "18.4.34", "angular-formio": "^4.11.5", "angular-lottie": "0.0.1", "angular5-csv": "^0.2.11", "chart.js": "^2.7.2", "eslint": "^5.1.0", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2pdf.js": "^0.9.2", "j3-integration-platform-ng": "^3.251.112-4", "j3-prc-components": "^3.2411.140", "j3-task-manager-ng": "^3.2308.333", "jibe-components": "3.251.422", "js2xmlparser": "^3.0.0", "json-to-table": "^4.0.0", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.25", "moment": "^2.24.0", "ngx-bootstrap": "^5.6.1", "ngx-moment": "^3.1.0", "ngx-sortablejs": "^3.1.4", "ngx-spellchecker": "^1.0.5", "odata-filter-builder": "^1.0.0-1", "primeflex": "^2.0.0", "primeicons": "^4.0.0", "primeng": "^9.1.3", "quill": "^1.3.6", "rxjs": "~6.6.0", "sortablejs": "^1.14.0", "tslib": "^1.10.0", "zone.js": "^0.11.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.901.12", "@angular-devkit/build-ng-packagr": "~0.901.13", "@angular/cli": "~9.1.12", "@angular/compiler-cli": "~9.1.12", "@types/node": "^12.11.1", "@types/resize-observer-browser": "^0.1.8", "codelyzer": "^5.1.2", "dotenv": "^16.0.3", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~5.0.0", "karma-chrome-launcher": "~3.1.0", "karma-jasmine": "~3.0.1", "karma-jasmine-html-reporter": "^1.4.2", "ng-packagr": "^9.0.0", "prettier": "2.8.3", "protractor": "~7.0.0", "rxjs-tslint-rules": "^4.34.8", "ts-node": "~8.3.0", "tslint": "~6.1.0", "tslint-config-prettier": "^1.18.0", "tslint-plugin-prettier": "^2.3.0", "typescript": "~3.8.3"}}